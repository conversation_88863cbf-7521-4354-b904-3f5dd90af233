<div class="multiple-membership-view" *ngIf="memberData">
  
  <!-- Compact View -->
  <div class="compact-view" *ngIf="showCompactView">
    <div class="member-summary">
      <div class="member-info">
        <div class="member-avatar" [ngStyle]="{'background-color': memberData.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
          {{ memberData.name.charAt(0) }}
        </div>
        <div class="member-details">
          <h6 class="member-name">{{ memberData.name }}</h6>
          <small class="member-phone">{{ memberData.phoneNumber }}</small>
        </div>
      </div>
      
      <div class="membership-summary">
        <span class="summary-text">{{ getCompactSummary() }}</span>
        <span class="modern-badge modern-badge-info" *ngIf="memberData.activeMemberships.length > 1">
          {{ memberData.activeMemberships.length }} üyelik
        </span>
      </div>
    </div>
  </div>

  <!-- Detailed View -->
  <div class="detailed-view" *ngIf="!showCompactView">
    
    <!-- Member Header -->
    <div class="member-header">
      <div class="member-info">
        <div class="member-avatar" [ngStyle]="{'background-color': memberData.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
          {{ memberData.name.charAt(0) }}
        </div>
        <div class="member-details">
          <h5 class="member-name">{{ memberData.name }}</h5>
          <p class="member-phone">{{ memberData.phoneNumber }}</p>
          <small class="last-update" *ngIf="memberData.lastUpdateDate">
            Son güncelleme: {{ formatDate(memberData.lastUpdateDate) }}
          </small>
        </div>
      </div>
      
      <div class="member-stats">
        <div class="stat-item">
          <fa-icon [icon]="faDumbbell" class="stat-icon text-primary"></fa-icon>
          <div class="stat-info">
            <span class="stat-value">{{ memberData.activeMemberships.length }}</span>
            <span class="stat-label">Aktif Üyelik</span>
          </div>
        </div>
        <div class="stat-item">
          <fa-icon [icon]="faClock" class="stat-icon text-success"></fa-icon>
          <div class="stat-info">
            <span class="stat-value">{{ memberData.totalRemainingDays }}</span>
            <span class="stat-label">Toplam Gün</span>
          </div>
        </div>
        <div class="stat-item">
          <fa-icon [icon]="faMoneyBillWave" class="stat-icon text-warning"></fa-icon>
          <div class="stat-info">
            <span class="stat-value">{{ formatCurrency(getTotalPaidAmount()) }}</span>
            <span class="stat-label">Toplam Ödeme</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Branch Summary -->
    <div class="branch-summary" *ngIf="getActiveBranches().length > 0">
      <h6 class="section-title">
        <fa-icon [icon]="faDumbbell" class="me-2"></fa-icon>
        Branş Özeti
      </h6>
      <div class="branch-cards">
        <div class="branch-card" *ngFor="let branchData of getActiveBranches()">
          <span class="modern-badge" [ngClass]="getBranchBadgeClass(branchData.branch)">
            {{ branchData.branch }}
          </span>
          <div class="branch-stats">
            <span class="branch-count">{{ branchData.count }} üyelik</span>
            <span class="branch-days">{{ branchData.totalDays }} gün</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Membership Details -->
    <div class="membership-details">
      <h6 class="section-title">
        <fa-icon [icon]="faCalendarAlt" class="me-2"></fa-icon>
        Üyelik Detayları
      </h6>
      
      <div class="membership-list">
        <div class="membership-card" *ngFor="let membership of getSortedMemberships(); let i = index">
          
          <!-- Card Header -->
          <div class="membership-header">
            <div class="membership-title">
              <span class="modern-badge" [ngClass]="getBranchBadgeClass(membership.branch)">
                {{ membership.branch }}
              </span>
              <h6 class="membership-name">{{ membership.typeName }}</h6>
              <span class="membership-duration">{{ membership.day }} gün</span>
            </div>
            
            <div class="membership-status">
              <span class="status-text" [ngClass]="getMembershipStatusClass(membership)">
                {{ getMembershipStatus(membership) }}
              </span>
              <fa-icon 
                *ngIf="membership.isFrozen" 
                [icon]="faSnowflake" 
                class="freeze-icon text-info"
                title="Dondurulmuş">
              </fa-icon>
            </div>
          </div>

          <!-- Card Body -->
          <div class="membership-body">
            <div class="membership-dates">
              <div class="date-item">
                <small class="date-label">Başlangıç</small>
                <span class="date-value">{{ formatDate(membership.startDate) }}</span>
              </div>
              <div class="date-item">
                <small class="date-label">Bitiş</small>
                <span class="date-value">{{ formatDate(membership.endDate) }}</span>
              </div>
              <div class="date-item">
                <small class="date-label">Kalan</small>
                <span class="date-value" [ngClass]="getRemainingDaysClass(membership.remainingDays)">
                  {{ membership.remainingDays }} gün
                </span>
              </div>
              <div class="date-item">
                <small class="date-label">Ücret</small>
                <span class="date-value text-success">{{ formatCurrency(membership.price) }}</span>
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="membership-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  [style.width.%]="(membership.remainingDays / membership.day) * 100"
                  [ngClass]="{
                    'progress-danger': membership.remainingDays <= 3,
                    'progress-warning': membership.remainingDays > 3 && membership.remainingDays <= 10,
                    'progress-success': membership.remainingDays > 10
                  }">
                </div>
              </div>
              <small class="progress-text">
                {{ membership.remainingDays }} / {{ membership.day }} gün kaldı
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions" *ngIf="showActions">
      <button class="modern-btn modern-btn-outline-primary modern-btn-sm">
        <fa-icon [icon]="faCalendarAlt" class="me-1"></fa-icon>
        Üyelik Ekle
      </button>
      <button class="modern-btn modern-btn-outline-info modern-btn-sm">
        <fa-icon [icon]="faSnowflake" class="me-1"></fa-icon>
        Dondur
      </button>
      <button class="modern-btn modern-btn-outline-danger modern-btn-sm">
        <fa-icon [icon]="faDumbbell" class="me-1"></fa-icon>
        Yönet
      </button>
    </div>
  </div>
</div>

<!-- No Data State -->
<div class="no-data" *ngIf="!memberData">
  <i class="fas fa-user-slash fa-2x text-muted mb-2"></i>
  <p class="text-muted">Üye verisi bulunamadı</p>
</div>
