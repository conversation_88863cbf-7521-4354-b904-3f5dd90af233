.multiple-membership-view {
  
  // Compact View Styles
  .compact-view {
    .member-summary {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.75rem;
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--primary);
        box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.1);
      }

      .member-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .member-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 1.1rem;
        }

        .member-details {
          .member-name {
            margin: 0;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
          }

          .member-phone {
            color: var(--text-secondary);
            font-size: 0.8rem;
          }
        }
      }

      .membership-summary {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .summary-text {
          font-weight: 500;
          color: var(--text-primary);
          font-size: 0.9rem;
        }
      }
    }
  }

  // Detailed View Styles
  .detailed-view {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;

    .member-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      background: linear-gradient(135deg, var(--primary-light), var(--primary));
      color: white;

      .member-info {
        display: flex;
        align-items: center;
        gap: 1rem;

        .member-avatar {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          font-weight: 600;
          font-size: 1.5rem;
          border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .member-details {
          .member-name {
            margin: 0 0 0.25rem 0;
            font-weight: 600;
            color: white;
          }

          .member-phone {
            margin: 0 0 0.25rem 0;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
          }

          .last-update {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
          }
        }
      }

      .member-stats {
        display: flex;
        gap: 1.5rem;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background: rgba(255, 255, 255, 0.1);
          padding: 0.75rem;
          border-radius: 8px;
          backdrop-filter: blur(10px);

          .stat-icon {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
          }

          .stat-info {
            display: flex;
            flex-direction: column;

            .stat-value {
              font-weight: 600;
              font-size: 1.1rem;
              color: white;
              line-height: 1;
            }

            .stat-label {
              font-size: 0.7rem;
              color: rgba(255, 255, 255, 0.8);
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
          }
        }
      }
    }

    .branch-summary {
      padding: 1.5rem;
      border-bottom: 1px solid var(--border-color);

      .section-title {
        margin-bottom: 1rem;
        color: var(--text-primary);
        font-weight: 600;
      }

      .branch-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;

        .branch-card {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem 1rem;
          background: var(--bg-light);
          border: 1px solid var(--border-color);
          border-radius: 8px;
          transition: all 0.2s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          .branch-stats {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;

            .branch-count,
            .branch-days {
              font-size: 0.8rem;
              color: var(--text-secondary);
            }

            .branch-days {
              font-weight: 600;
              color: var(--text-primary);
            }
          }
        }
      }
    }

    .membership-details {
      padding: 1.5rem;

      .section-title {
        margin-bottom: 1rem;
        color: var(--text-primary);
        font-weight: 600;
      }

      .membership-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .membership-card {
          background: var(--bg-light);
          border: 1px solid var(--border-color);
          border-radius: 10px;
          overflow: hidden;
          transition: all 0.2s ease;

          &:hover {
            border-color: var(--primary);
            box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.1);
          }

          .membership-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--card-bg);
            border-bottom: 1px solid var(--border-color);

            .membership-title {
              display: flex;
              align-items: center;
              gap: 0.75rem;

              .membership-name {
                margin: 0;
                font-weight: 600;
                color: var(--text-primary);
              }

              .membership-duration {
                font-size: 0.8rem;
                color: var(--text-secondary);
                background: var(--bg-light);
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
              }
            }

            .membership-status {
              display: flex;
              align-items: center;
              gap: 0.5rem;

              .status-text {
                font-weight: 500;
                font-size: 0.9rem;
              }

              .freeze-icon {
                font-size: 1.1rem;
              }
            }
          }

          .membership-body {
            padding: 1rem;

            .membership-dates {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
              gap: 1rem;
              margin-bottom: 1rem;

              .date-item {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;

                .date-label {
                  color: var(--text-secondary);
                  font-size: 0.8rem;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                }

                .date-value {
                  font-weight: 600;
                  color: var(--text-primary);
                  font-size: 0.9rem;
                }
              }
            }

            .membership-progress {
              .progress-bar {
                width: 100%;
                height: 6px;
                background: var(--border-color);
                border-radius: 3px;
                overflow: hidden;
                margin-bottom: 0.5rem;

                .progress-fill {
                  height: 100%;
                  border-radius: 3px;
                  transition: width 0.3s ease;

                  &.progress-success {
                    background: var(--success);
                  }

                  &.progress-warning {
                    background: var(--warning);
                  }

                  &.progress-danger {
                    background: var(--danger);
                  }
                }
              }

              .progress-text {
                color: var(--text-secondary);
                font-size: 0.8rem;
              }
            }
          }
        }
      }
    }

    .quick-actions {
      padding: 1.5rem;
      border-top: 1px solid var(--border-color);
      background: var(--bg-light);
      display: flex;
      gap: 0.75rem;
      justify-content: center;
    }
  }

  // No Data State
  .no-data {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .multiple-membership-view {
    .detailed-view {
      .member-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;

        .member-stats {
          justify-content: center;
        }
      }

      .branch-summary .branch-cards {
        justify-content: center;
      }

      .membership-details .membership-list .membership-card {
        .membership-header {
          flex-direction: column;
          gap: 0.75rem;
          align-items: flex-start;
        }

        .membership-body .membership-dates {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      .quick-actions {
        flex-direction: column;

        .modern-btn {
          width: 100%;
        }
      }
    }
  }
}

// Dark Mode Support
[data-theme="dark"] {
  .multiple-membership-view {
    .detailed-view .member-header {
      background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    }
  }
}
