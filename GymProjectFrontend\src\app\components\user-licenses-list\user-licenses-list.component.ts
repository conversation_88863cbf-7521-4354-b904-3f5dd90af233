import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { UserLicenseService } from '../../services/user-license.service';
import { DialogService } from '../../services/dialog.service';
import { UserLicenseDto } from '../../models/UserLicenseDto';
import { LicensePurchaseComponent } from '../license-purchase/license-purchase.component';
import { ExtendLicenseComponent } from '../extend-license/extend-license.component';

@Component({
  selector: 'app-user-licenses-list',
  templateUrl: './user-licenses-list.component.html',
  styleUrls: ['./user-licenses-list.component.css'],
  standalone:false
})
export class UserLicensesListComponent implements OnInit {
  userLicenses: UserLicenseDto[] = [];
  isLoading = false;
  displayedColumns: string[] = ['userName', 'packageName', 'role', 'startDate', 'endDate', 'remainingDays', 'actions'];
  
  constructor(
    private userLicenseService: UserLicenseService,
    private dialogService: DialogService,
    private dialog: MatDialog,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.loadUserLicenses();
  }

  loadUserLicenses(): void {
    this.isLoading = true;
    this.userLicenseService.getAll().subscribe({
      next: (response) => {
        this.userLicenses = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Kullanıcı lisansları yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  openPurchaseDialog(): void {
    const dialogRef = this.dialog.open(LicensePurchaseComponent, {
      width: '600px'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUserLicenses();
      }
    });
  }

  openExtendDialog(userLicense: UserLicenseDto): void {
    const dialogRef = this.dialog.open(ExtendLicenseComponent, {
      width: '400px',
      data: { userLicense }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadUserLicenses();
      }
    });
  }

  revokeLicense(id: number, userName: string): void {
    this.dialogService.confirmRevoke(userName).subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.userLicenseService.revokeLicense(id).subscribe({
          next: (response) => {
            this.toastr.success(response.message, 'Başarılı');
            this.loadUserLicenses();
          },
          error: (error) => {
            this.toastr.error('Lisans iptal edilirken bir hata oluştu', 'Hata');
          }
        });
      }
    });
  }

  getRemainingDaysClass(days: number): string {
    if (days <= 0) {
      return 'text-danger';
    } else if (days <= 7) {
      return 'text-warning';
    } else {
      return 'text-success';
    }
  }
}
