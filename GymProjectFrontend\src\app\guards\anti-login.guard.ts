import { inject, PLATFORM_ID } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { isPlatformBrowser } from '@angular/common';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

export const antiLoginGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);
  const platformId = inject(PLATFORM_ID);
  const isBrowser = isPlatformBrowser(platformId);

  // If not in browser, allow navigation to login page
  if (!isBrowser) {
    return true;
  }

  // If already authenticated, redirect to main page
  if (authService.isAuthenticated()) {
    router.navigate(['/todayentries']);
    return false;
  }

  // Only access localStorage in browser environment
  if (isBrowser) {
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refreshToken');
    
    // If we have tokens, try to refresh silently without showing login page
    if (token && refreshToken) {
      // Return an observable to prevent the guard from completing until token refresh is done
      return new Observable<boolean>(observer => {
        // Add a loading state to prevent flashing of login page
        document.body.classList.add('refreshing-token');
        
        authService.refreshToken().pipe(
          map(response => {
            if (response.success) {
              document.body.classList.remove('refreshing-token');
              router.navigate(['/todayentries']);
              observer.next(false);
              observer.complete();
              return false;
            } else {
              document.body.classList.remove('refreshing-token');
              observer.next(true);
              observer.complete();
              return true;
            }
          }),
          catchError(() => {
            document.body.classList.remove('refreshing-token');
            observer.next(true);
            observer.complete();
            return of(true);
          })
        ).subscribe();
      });
    }
  }
  
  return true;
};
