import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './baseApiService';
import { SingleResponseModel } from '../models/singleResponseModel';
import { 
  MemberActiveMemberships, 
  MembershipDeleteRequest, 
  MembershipDeleteResult 
} from '../models/membership-delete';

@Injectable({
  providedIn: 'root'
})
export class MembershipDeleteService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  /**
   * Üyenin tüm aktif üyeliklerini güvenli silme için listeler
   */
  getMemberActiveMembershipsForDelete(memberId: number): Observable<SingleResponseModel<MemberActiveMemberships>> {
    return this.httpClient.get<SingleResponseModel<MemberActiveMemberships>>(
      `${this.apiUrl}member/getactivemembershipsfordelete/${memberId}`
    );
  }

  /**
   * Çoklu üyelik güvenli silme işlemi
   */
  deleteMultipleMemberships(deleteRequest: MembershipDeleteRequest): Observable<SingleResponseModel<MembershipDeleteResult>> {
    return this.httpClient.post<SingleResponseModel<MembershipDeleteResult>>(
      `${this.apiUrl}membership/deletemultiple`,
      deleteRequest
    );
  }
}
