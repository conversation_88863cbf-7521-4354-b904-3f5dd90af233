<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <div [class.content-blur]="isLoading">
    <!-- Dashboard Header -->
    <div class="dashboard-header mb-4" *ngIf="!isSearched">
      <div class="modern-card visitor-summary-card">
        <div class="card-body text-center">
          <div class="visitor-icon">
            <i class="fas fa-users"></i>
          </div>
          <h5 class="card-title">Günlük Toplam Ziyaretçi</h5>
          <div class="visitor-count">{{ getTotalVisitorsToday() }}</div>
          <p class="date-display">{{ selectedDate | date : "dd/MM/yyyy" }}</p>
        </div>
      </div>
    </div>

    <!-- <PERSON><PERSON> ve Filtreleme -->
    <div class="modern-card mb-4">
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-search me-2"></i>
              <span class="modern-form-label mb-0">Üye Ara</span>
            </div>
            <div class="search-container d-flex">
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-user"></i></span>
                <input
                  type="text"
                  class="form-control search-input"
                  [matAutocomplete]="auto"
                  [formControl]="memberControl"
                  placeholder="İsim veya telefon numarası ile arama yapın..."
                />
              </div>
              <button
                class="btn btn-primary search-btn ms-2"
                type="button"
                (click)="searchMember()"
              >
                Ara
              </button>
              <button
                class="btn btn-outline-secondary search-btn ms-2"
                type="button"
                *ngIf="memberControl.value"
                (click)="clearSearch()"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
            <mat-autocomplete
              #auto="matAutocomplete"
              [displayWith]="displayMember"
            >
              <mat-option
                *ngFor="let member of filteredMembers | async"
                [value]="member"
              >
                {{ member.name }} - {{ member.phoneNumber }}
              </mat-option>
            </mat-autocomplete>
          </div>
          <div class="col-md-6" *ngIf="!isSearched">
            <div class="d-flex align-items-center mb-2">
              <i class="fas fa-calendar-alt me-2"></i>
              <span class="modern-form-label mb-0">Tarih Seçin</span>
            </div>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-calendar"></i></span>
              <input
                type="date"
                id="dateFilter"
                class="form-control"
                [(ngModel)]="selectedDate"
                (change)="onDateChange()"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Giriş/Çıkış Tablosu -->
    <div class="modern-card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-list me-3 ms-3"></i>
          Giriş/Çıkış Kayıtları
        </h5>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="modern-table">
            <thead>
              <tr>
                <th>Üye Adı</th>
                <th>Telefon</th>
                <th>Giriş Tarihi</th>
                <th>Giriş Saati</th>
                <th>Çıkış Saati</th>
                <th>Spor Süresi</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let entry of entries" [ngClass]="{'active-entry': !entry.exitTime}">
                <td>
                  <div class="member-info">
                    <div class="member-avatar" [style.backgroundColor]="getAvatarColor(entry.name)">
                      {{ getInitials(entry.name) }}
                    </div>
                    <div>
                      {{ entry.name }}
                      <span
                        class="modern-badge ms-2"
                        *ngIf="!isSearched"
                        [ngClass]="{
                          'modern-badge-danger': entry.remainingDays <= 7,
                          'modern-badge-success': entry.remainingDays >= 8
                        }"
                      >
                        {{ entry.remainingDays }} Gün
                      </span>
                    </div>
                  </div>
                </td>
                <td>{{ entry.phoneNumber }}</td>
                <td>{{ entry.entryTime | date : "dd/MM/yyyy" }}</td>
                <td>{{ entry.entryTime | date : "HH:mm:ss" }}</td>
                <td>
                  <span *ngIf="entry.exitTime && !shouldShowQRWarning(entry)">
                    {{ entry.exitTime | date : "HH:mm:ss" }}
                  </span>
                  <span *ngIf="shouldShowQRWarning(entry)" class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i> Çıkış Yapılmadı
                  </span>
                  <span *ngIf="!entry.exitTime" class="modern-badge modern-badge-success">
                    <i class="fas fa-running me-1"></i> Aktif
                  </span>
                </td>
                <td>
                  <span [ngClass]="{'text-success': getDurationClass(entry) === 'success',
                                    'text-warning': getDurationClass(entry) === 'warning',
                                    'text-danger': getDurationClass(entry) === 'danger'}">
                    {{
                      entry.exitTime
                        ? calculateDuration(entry.entryTime, entry.exitTime)
                        : "-"
                    }}
                  </span>
                </td>
              </tr>
              <tr *ngIf="entries.length === 0">
                <td colspan="6" class="text-center py-4">
                  <div class="empty-state">
                    <i class="fas fa-search fa-3x mb-3"></i>
                    <h5>Kayıt bulunamadı</h5>
                    <p class="text-muted">Seçilen kriterlere uygun kayıt bulunamadı.</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
