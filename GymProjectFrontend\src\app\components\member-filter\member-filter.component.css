/* Member Filter Component Styles */

/* Content Blur Effect */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Filter Card Styles */
.filter-card {
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.filter-card:hover {
  transform: translateY(-5px);
}

.filter-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filter-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.filter-title {
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

/* Modern Radio Buttons */
.modern-radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.modern-radio {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.modern-radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.95rem;
  margin-bottom: 0;
  transition: all 0.2s ease;
}

.radio-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid var(--secondary);
  margin-right: 0.75rem;
  position: relative;
  transition: all 0.2s ease;
}

.radio-icon:after {
  content: '';
  position: absolute;
  display: none;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--primary);
}

.modern-radio-input:checked ~ .modern-radio-label .radio-icon {
  border-color: var(--primary);
}

.modern-radio-input:checked ~ .modern-radio-label .radio-icon:after {
  display: block;
}

.modern-radio-input:focus ~ .modern-radio-label .radio-icon {
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-radio-label:hover {
  color: var(--primary);
}

/* Member List Card */
.member-list-card {
  height: 100%;
}

/* Modern Search Input */
.search-container {
  position: relative;
  width: 300px;
}

.modern-search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-search-input input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: none;
  border-radius: var(--border-radius-md);
  background-color: rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.modern-search-input input:focus {
  outline: none;
  background-color: rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--secondary);
  font-size: 0.95rem;
  pointer-events: none;
}

/* Table Container */
.table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius-md);
}

/* Member Name with Avatar */
.member-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Remaining Days Styling */
.remaining-days {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

/* Pagination Container */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

/* Gender Chart Card */
.gender-chart-card {
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.gender-chart-card:hover {
  transform: translateY(-5px);
}

.gender-chart-card .modern-card-body {
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 220px;
}

/* Dark Mode Support */
[data-theme="dark"] .filter-section {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modern-search-input input {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-search-input input:focus {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .gender-chart-container {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-card {
  background-color: #2d3748;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-card-header {
  border-bottom-color: #4a5568;
}

[data-theme="dark"] .modern-table th {
  background-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-table td {
  color: #e2e8f0;
  border-bottom-color: #4a5568;
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-pagination .modern-page-link {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-pagination .modern-page-link:hover {
  background-color: #4a5568;
}

[data-theme="dark"] .modern-pagination .modern-page-item.disabled .modern-page-link {
  background-color: #2d3748;
  color: #718096;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search-container {
    width: 100%;
    margin-top: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    margin-left: 0 !important;
    margin-top: 0.5rem;
  }
  
  .action-buttons button:first-child {
    margin-top: 0;
  }
  
  .member-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* YENİ BASIT FİLTRELEME STİLLERİ */
.filter-item {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
  background: var(--card-bg);
}

.filter-item:hover {
  border-color: var(--primary);
  box-shadow: 0 2px 8px rgba(67, 97, 238, 0.1);
}

.filter-item.active {
  border-color: var(--primary);
  background: var(--primary-light);
}

.filter-button {
  width: 100%;
  border: none;
  background: transparent;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
}

.filter-button:hover {
  background: var(--bg-light);
}

.filter-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.filter-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.filter-status {
  display: flex;
  align-items: center;
}

.filter-actions {
  display: flex;
  align-items: center;
}

.dropdown-toggle {
  background: none;
  border: none;
  padding: 8px;
  margin-left: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.dropdown-toggle:hover {
  background: var(--primary-light);
  color: var(--primary);
}

.dropdown-toggle.expanded {
  transform: rotate(180deg);
}

/* Paket Listesi - BASIT VE STABİL */
.package-list {
  background: var(--bg-light);
  border-top: 1px solid var(--border-color);
  padding: 0.5rem;
  /* Layout shift'i engelle */
  position: relative;
}

.package-item {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-bottom: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
  background: var(--card-bg);
}

.package-item:hover {
  border-color: var(--success);
  transform: translateX(4px);
}

.package-item.active {
  border-color: var(--success);
  background: var(--success);
}

.package-button {
  width: 100%;
  border: none;
  background: transparent;
  padding: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
}

.package-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.package-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.package-info {
  display: flex;
  flex-direction: column;
  margin-left: 0.5rem;
}

.package-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.package-duration {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.package-status {
  display: flex;
  align-items: center;
}

.package-item.active .package-button {
  color: white;
}

.package-item.active .package-duration {
  color: rgba(255, 255, 255, 0.8);
}

/* Eski accordion stilleri - uyumluluk için */
.branch-accordion-item {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.branch-accordion-item.active {
  border-color: var(--primary);
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.1);
}

.branch-accordion-item.expanded .transition-icon {
  transform: rotate(180deg);
}

.branch-accordion-item .branch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  background: var(--card-bg);
  transition: all 0.2s ease;
}

.branch-accordion-item .branch-header .branch-select-area {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.branch-accordion-item .branch-header .branch-select-area:hover {
  background: var(--bg-light);
}

.branch-accordion-item .branch-header .dropdown-toggle-area {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 100%;
  cursor: pointer;
  border-left: 1px solid var(--border-color);
  transition: all 0.2s ease;
  background: var(--card-bg);
}

.branch-accordion-item .branch-header .dropdown-toggle-area:hover {
  background: var(--primary-light);
  color: var(--primary);
}

.branch-accordion-item .branch-header .branch-info {
  display: flex;
  align-items: center;
}

.branch-accordion-item .branch-header .branch-info .branch-name {
  font-weight: 600;
  color: var(--text-primary);
}

.branch-accordion-item .branch-header .branch-actions {
  display: flex;
  align-items: center;
}

.branch-accordion-item .branch-header .branch-actions .transition-icon {
  transition: transform 0.3s ease;
  color: var(--text-secondary);
}

.branch-accordion-item .package-list {
  background: var(--bg-light);
  border-top: 1px solid var(--border-color);
}

.branch-accordion-item .package-list .package-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.branch-accordion-item .package-list .package-header .package-all {
  width: 100%;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.branch-accordion-item .package-list .package-header .package-all:hover {
  border-color: var(--primary);
  background: var(--primary-light);
}

.branch-accordion-item .package-list .package-header .package-all.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.branch-accordion-item .package-list .package-items {
  padding: 0.5rem 1rem 1rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.branch-accordion-item .package-list .package-items .package-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.branch-accordion-item .package-list .package-items .package-item:hover {
  border-color: var(--success);
  transform: translateX(4px);
}

.branch-accordion-item .package-list .package-items .package-item.active {
  background: var(--success);
  color: white;
  border-color: var(--success);
}

.branch-accordion-item .package-list .package-items .package-item .package-details {
  display: flex;
  flex-direction: column;
  margin-left: 0.5rem;
}

.branch-accordion-item .package-list .package-items .package-item .package-details .package-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.branch-accordion-item .package-list .package-items .package-item .package-details .package-duration {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.branch-accordion-item .package-list .package-items .package-item.active .package-details .package-duration {
  color: rgba(255, 255, 255, 0.8);
}

.active-filter-summary {
  background: var(--primary-light);
  border: 1px solid var(--primary);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.active-filter-summary .filter-summary-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

.active-filter-summary .filter-summary-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-primary);
  font-weight: 500;
}

/* Dark Mode Support */
[data-theme="dark"] .branch-accordion-item {
  border-color: var(--border-color-dark);
  background: var(--card-bg-dark);
}

[data-theme="dark"] .branch-accordion-item .branch-header .branch-select-area {
  background: var(--card-bg-dark);
  color: var(--text-primary-dark);
}

[data-theme="dark"] .branch-accordion-item .branch-header .branch-select-area:hover {
  background: var(--bg-light-dark);
}

[data-theme="dark"] .branch-accordion-item .branch-header .dropdown-toggle-area {
  background: var(--card-bg-dark);
  border-color: var(--border-color-dark);
  color: var(--text-primary-dark);
}

[data-theme="dark"] .branch-accordion-item .branch-header .dropdown-toggle-area:hover {
  background: var(--primary-dark);
  color: var(--primary-light);
}

[data-theme="dark"] .branch-accordion-item .package-list {
  background: var(--bg-light-dark);
  border-color: var(--border-color-dark);
}

[data-theme="dark"] .branch-accordion-item .package-list .package-header .package-all {
  background: var(--card-bg-dark);
  border-color: var(--border-color-dark);
  color: var(--text-primary-dark);
}

[data-theme="dark"] .branch-accordion-item .package-list .package-header .package-all:hover {
  border-color: var(--primary);
  background: var(--primary-dark);
}

[data-theme="dark"] .branch-accordion-item .package-list .package-items .package-item {
  background: var(--card-bg-dark);
  border-color: var(--border-color-dark);
  color: var(--text-primary-dark);
}

[data-theme="dark"] .branch-accordion-item .package-list .package-items .package-item:hover {
  border-color: var(--success);
}

[data-theme="dark"] .branch-accordion-item .package-list .package-items .package-item .package-details .package-duration {
  color: var(--text-secondary-dark);
}

[data-theme="dark"] .active-filter-summary {
  background: var(--primary-dark);
  border-color: var(--primary);
}

[data-theme="dark"] .active-filter-summary .filter-summary-header {
  color: var(--primary-light);
}

[data-theme="dark"] .active-filter-summary .filter-summary-content {
  color: var(--text-primary-dark);
}

/* Spam koruması için loading state */
.branch-accordion-item.processing {
  pointer-events: none;
  opacity: 0.7;
}

.branch-accordion-item.processing .branch-header {
  cursor: not-allowed;
}

.package-item.processing {
  pointer-events: none;
  opacity: 0.7;
  cursor: not-allowed;
}