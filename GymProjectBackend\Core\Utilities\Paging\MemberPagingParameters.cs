﻿namespace Core.Utilities.Paging
{
    // Core/Utilities/Paging/MemberPagingParameters.cs
    public class MemberPagingParameters : PagingParameters
    {
        public int? Gender { get; set; }
        public string Branch { get; set; } = ""; // Default boş string
        public bool? IsActive { get; set; }
        public string SearchText { get; set; } = ""; // Default boş string

        // Yeni paket bazlı filtreleme alanları
        public int? MembershipTypeID { get; set; }
        public string PackageGroup { get; set; } = ""; // "1 Aylık", "3 Aylık" vs
        public int? MinDays { get; set; }
        public int? MaxDays { get; set; }
    }
}
