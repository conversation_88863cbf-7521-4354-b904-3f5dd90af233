.rate-limit-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
}

/* Header */
.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.page-description {
  font-size: 1.1rem;
  color: var(--text-muted);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Cards */
.test-config-card,
.status-card,
.results-card,
.info-card {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.card-title,
.status-title,
.results-title,
.info-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Configuration Grid */
.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.95rem;
}

.form-select,
.form-input {
  padding: 12px 15px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--input-bg);
  color: var(--text-color);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-select:focus,
.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.form-select:disabled,
.form-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Test Controls */
.test-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-2px);
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-2px);
}

/* Status Grid */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-label {
  font-weight: 600;
  color: var(--text-muted);
  font-size: 0.9rem;
}

.status-value {
  font-size: 1.5rem;
  font-weight: 700;
}

.status-value.success {
  color: #28a745;
}

.status-value.rate-limited {
  color: #ffc107;
}

.status-value.error {
  color: #dc3545;
}

/* Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.progress-text {
  font-weight: 600;
  color: var(--text-color);
  min-width: 80px;
}

/* Results Table */
.results-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.results-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--card-bg);
}

.results-table th,
.results-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.results-table th {
  background: var(--primary-color);
  color: white;
  font-weight: 600;
  position: sticky;
  top: 0;
}

.results-table tr:hover {
  background: var(--hover-bg);
}

.message-cell {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Status Badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-success .status-badge,
.status-badge.status-success {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.status-rate-limited .status-badge,
.status-badge.status-rate-limited {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.status-error .status-badge,
.status-badge.status-error {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

/* Info Content */
.info-content h4 {
  color: var(--primary-color);
  margin: 20px 0 10px 0;
  font-size: 1.1rem;
}

.info-content ul {
  margin: 10px 0;
  padding-left: 20px;
}

.info-content li {
  margin: 8px 0;
  line-height: 1.5;
}

.warning-box {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid #ffc107;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.warning-box i {
  color: #ffc107;
  margin-top: 2px;
}

/* Responsive */
@media (max-width: 768px) {
  .rate-limit-test-container {
    padding: 15px;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .test-controls {
    flex-direction: column;
  }
  
  .btn {
    justify-content: center;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .progress-container {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .results-table {
    font-size: 0.9rem;
  }
  
  .results-table th,
  .results-table td {
    padding: 8px 10px;
  }
}
