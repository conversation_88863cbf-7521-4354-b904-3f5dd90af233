import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './baseApiService';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { PaginatedResult } from '../models/pagination';
import { 
  BranchPackageFilter, 
  AdvancedMemberFilter, 
  MemberMultipleMembership 
} from '../models/branch-package-filter';
import { MemberFilter } from '../models/memberFilter';

@Injectable({
  providedIn: 'root'
})
export class AdvancedMemberFilterService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  /**
   * Branş bazlı paket filtreleme bilgilerini getirir
   * İki seviyeli filtreleme sistemi için kullanılır
   */
  getBranchPackageFilters(): Observable<ListResponseModel<BranchPackageFilter>> {
    return this.httpClient.get<ListResponseModel<BranchPackageFilter>>(
      `${this.apiUrl}member/getbranchpackagefilters`
    );
  }

  /**
   * Çoklu üyelik görünümü için üye detaylarını getirir
   */
  getMembersWithMultipleMemberships(branch?: string): Observable<ListResponseModel<MemberMultipleMembership>> {
    let params = new HttpParams();
    if (branch) {
      params = params.set('branch', branch);
    }

    return this.httpClient.get<ListResponseModel<MemberMultipleMembership>>(
      `${this.apiUrl}member/getmemberswithmultiplememberships`,
      { params }
    );
  }

  /**
   * Gelişmiş filtreleme ile üye detaylarını getirir
   */
  getMemberDetailsAdvanced(filter: AdvancedMemberFilter): Observable<SingleResponseModel<PaginatedResult<MemberFilter>>> {
    let params = new HttpParams()
      .set('pageNumber', (filter.pageNumber || 1).toString())
      .set('pageSize', (filter.pageSize || 50).toString());

    if (filter.searchText) params = params.set('searchText', filter.searchText);
    if (filter.gender !== undefined) params = params.set('gender', filter.gender.toString());
    if (filter.branch) params = params.set('branch', filter.branch);
    if (filter.membershipTypeID) params = params.set('membershipTypeID', filter.membershipTypeID.toString());
    if (filter.packageGroup) params = params.set('packageGroup', filter.packageGroup);
    if (filter.minDays) params = params.set('minDays', filter.minDays.toString());
    if (filter.maxDays) params = params.set('maxDays', filter.maxDays.toString());

    return this.httpClient.get<SingleResponseModel<PaginatedResult<MemberFilter>>>(
      `${this.apiUrl}member/getmemberdetailsadvanced`,
      { params }
    );
  }
}
