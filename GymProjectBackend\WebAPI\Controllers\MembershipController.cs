﻿using Business.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MembershipController : ControllerBase
    {
        IMembershipService _membershipService;

        public MembershipController(IMembershipService membershipService)
        {
            _membershipService = membershipService;
        }
        [HttpPost("cancel-freeze/{id}")]
        public IActionResult CancelFreeze(int id)
        {
            var result = _membershipService.CancelFreeze(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("reactivate-from-today/{id}")]
        public IActionResult ReactivateFromToday(int id)
        {
            var result = _membershipService.ReactivateFromToday(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpPost("freeze")]
        public IActionResult FreezeMembership([FromBody] MembershipFreezeRequestDto freezeRequest)
        {
            var result = _membershipService.FreezeMembership(freezeRequest);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("unfreeze/{id}")]
        public IActionResult UnfreezeMembership(int id)
        {
            var result = _membershipService.UnfreezeMembership(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("frozen")]
        public IActionResult GetFrozenMemberships()
        {
            var result = _membershipService.GetFrozenMemberships();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _membershipService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add(MembershipAddDto membership)
        {
            var result = _membershipService.Add(membership);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _membershipService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }

        [HttpPost("update")]
        public IActionResult Update(MembershipUpdateDto membership)
        {
            var result = _membershipService.Update(membership);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }

        [HttpGet("getbymembershipid")]
        public IActionResult GetByMemberId(int id)
        {
            var result = _membershipService.GetByMembershipId(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        [HttpGet("getlastmembershipinfo/{memberId}")]
        public IActionResult GetLastMembershipInfo(int memberId)
        {
            var result = _membershipService.GetLastMembershipInfo(memberId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Çoklu üyelik güvenli silme işlemi
        /// Üyenin seçilen üyeliklerini veya tüm üyeliklerini güvenli şekilde siler
        /// </summary>
        [HttpPost("deletemultiple")]
        public IActionResult DeleteMultipleMemberships([FromBody] MembershipDeleteRequestDto deleteRequest)
        {
            if (deleteRequest == null)
            {
                return BadRequest("Silme isteği boş olamaz.");
            }

            var result = _membershipService.DeleteMultipleMemberships(deleteRequest);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}