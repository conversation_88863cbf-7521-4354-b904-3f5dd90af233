using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMemberDal : EfCompanyEntityRepositoryBase<Member, GymContext>, IMemberDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfMemberDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public MemberDetailWithHistoryDto GetMemberDetailById(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                // Get member basic information
                var member = context.Members
                    .FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);

                if (member == null)
                {
                    return null;
                }

                // Get memberships with membership type details
                var memberships = (from ms in context.Memberships
                                  join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                  where ms.MemberID == memberId
                                        && ms.CompanyID == companyId
                                        && mt.CompanyID == companyId
                                  select new MembershipHistoryDto
                                  {
                                      MembershipID = ms.MembershipID,
                                      MembershipTypeID = ms.MembershipTypeID,
                                      TypeName = mt.TypeName,
                                      Branch = mt.Branch,
                                      Day = mt.Day,
                                      Price = mt.Price,
                                      StartDate = ms.StartDate,
                                      EndDate = ms.EndDate,
                                      IsActive = ms.IsActive == true,
                                      IsFrozen = ms.IsFrozen,
                                      FreezeStartDate = ms.FreezeStartDate,
                                      FreezeEndDate = ms.FreezeEndDate,
                                      FreezeDays = ms.FreezeDays,
                                      OriginalEndDate = ms.OriginalEndDate,
                                      CreationDate = ms.CreationDate
                                  }).ToList();

                // Get payments with membership type details
                var payments = (from p in context.Payments
                               join ms in context.Memberships on p.MemberShipID equals ms.MembershipID
                               join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                               where ms.MemberID == memberId
                                     && p.CompanyID == companyId
                                     && ms.CompanyID == companyId
                                     && mt.CompanyID == companyId
                               select new PaymentHistoryItemDto
                               {
                                   PaymentID = p.PaymentID,
                                   MembershipID = ms.MembershipID,
                                   MembershipTypeName = mt.TypeName,
                                   Branch = mt.Branch,
                                   PaymentDate = p.PaymentDate,
                                   PaymentAmount = p.PaymentAmount,
                                   PaymentMethod = p.PaymentMethod,
                                   PaymentStatus = p.PaymentStatus
                               }).ToList();

                // Get entry/exit history with membership type details
                var entryExitHistory = (from eh in context.EntryExitHistories
                                       join ms in context.Memberships on eh.MembershipID equals ms.MembershipID
                                       join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                       where ms.MemberID == memberId
                                             && eh.CompanyID == companyId
                                             && ms.CompanyID == companyId
                                             && mt.CompanyID == companyId
                                       select new EntryExitHistoryItemDto
                                       {
                                           EntryExitID = eh.EntryExitID,
                                           MembershipID = ms.MembershipID,
                                           MembershipTypeName = mt.TypeName,
                                           Branch = mt.Branch,
                                           EntryDate = eh.EntryDate,
                                           ExitDate = eh.ExitDate
                                       }).ToList();

                // Get membership freeze history with membership type details
                var freezeHistory = (from fh in context.MembershipFreezeHistory
                                    join ms in context.Memberships on fh.MembershipID equals ms.MembershipID
                                    join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                    where ms.MemberID == memberId
                                          && fh.CompanyID == companyId
                                          && ms.CompanyID == companyId
                                          && mt.CompanyID == companyId
                                    select new MembershipFreezeHistoryItemDto
                                    {
                                        FreezeHistoryID = fh.FreezeHistoryID,
                                        MembershipID = ms.MembershipID,
                                        MembershipTypeName = mt.TypeName,
                                        Branch = mt.Branch,
                                        StartDate = fh.StartDate,
                                        PlannedEndDate = fh.PlannedEndDate,
                                        ActualEndDate = fh.ActualEndDate,
                                        FreezeDays = fh.FreezeDays,
                                        UsedDays = fh.UsedDays,
                                        CancellationType = fh.CancellationType
                                    }).ToList();

                // Get the last membership end date
                var lastMembershipEndDate = context.Memberships
                    .Where(ms => ms.MemberID == memberId && ms.CompanyID == companyId)
                    .OrderByDescending(ms => ms.EndDate)
                    .Select(ms => (DateTime?)ms.EndDate) // Nullable DateTime'a cast et
                    .FirstOrDefault();

                // Create and return the member detail DTO
                return new MemberDetailWithHistoryDto
                {
                    MemberID = member.MemberID,
                    UserID = member.UserID, // Profil fotoğrafı için eklendi
                    Name = member.Name,
                    Gender = member.Gender,
                    PhoneNumber = member.PhoneNumber,
                    Adress = member.Adress,
                    BirthDate = member.BirthDate,
                    Email = member.Email,
                    IsActive = member.IsActive,
                    ScanNumber = member.ScanNumber,
                    Balance = member.Balance,
                    CreationDate = member.CreationDate,
                    Memberships = memberships,
                    Payments = payments,
                    EntryExitHistory = entryExitHistory,
                    FreezeHistory = freezeHistory,
                    LastMembershipEndDate = lastMembershipEndDate // Son üyelik bitiş tarihini ekle
                };
            }
        }

        public PaginatedResult<Member> GetAllPaginated(MemberPagingParameters parameters)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var query = context.Members.Where(x => x.IsActive == true && x.CompanyID == companyId);  // IsActive ve CompanyID kontrolü eklendi

                // Filtreleme
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(x =>
                        x.Name.Contains(parameters.SearchText) ||
                        x.PhoneNumber.Contains(parameters.SearchText));
                }

                if (parameters.Gender.HasValue)
                {
                    // Gelen int? değerini byte'a cast et
                    byte genderByte = (byte)parameters.Gender.Value;
                    query = query.Where(x => x.Gender == genderByte);
                }

                // Sıralama
                query = query.OrderByDescending(x => x.CreationDate);

                return query.ToPaginatedResult(parameters.PageNumber, parameters.PageSize);
            }
        }

        public List<MemberBirthdayDto> GetUpcomingBirthdays(int days)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var today = DateTime.Now.Date;

                // Doğum günü yaklaşan üyeleri getir
                var result = context.Members
                    .Where(m => m.IsActive == true && m.CompanyID == companyId && m.BirthDate != null)
                    .AsEnumerable() // Sonraki işlemler için hafızaya al
                    .Select(m => new
                    {
                        Member = m,
                        // Doğum gününe kalan gün sayısını hesapla
                        DaysUntilBirthday = CalculateDaysUntilBirthday(m.BirthDate, today)
                    })
                    .Where(x => x.DaysUntilBirthday <= days) // Belirtilen gün sayısı içinde doğum günü olanları filtrele
                    .OrderBy(x => x.DaysUntilBirthday) // Doğum gününe kalan gün sayısına göre sırala
                    .Select(x => new MemberBirthdayDto
                    {
                        MemberID = x.Member.MemberID,
                        Name = x.Member.Name,
                        PhoneNumber = x.Member.PhoneNumber,
                        BirthDate = x.Member.BirthDate
                    })
                    .ToList();

                return result;
            }
        }

        // Doğum gününe kalan gün sayısını hesaplayan yardımcı metod
        private int CalculateDaysUntilBirthday(DateOnly? birthDate, DateTime today)
        {
            if (!birthDate.HasValue) return int.MaxValue; // Doğum tarihi yoksa en sona koy

            var birth = birthDate.Value;

            // Bu yılki doğum günü
            var birthThisYear = new DateTime(today.Year, birth.Month, birth.Day);

            // Eğer bu yılki doğum günü geçtiyse, gelecek yılki doğum gününü hesapla
            if (birthThisYear < today)
            {
                birthThisYear = birthThisYear.AddYears(1);
            }

            // Doğum gününe kalan gün sayısı
            var daysUntil = (int)(birthThisYear - today).TotalDays;

            return daysUntil;
        }

        public PaginatedResult<MemberFilter> GetMemberDetailsPaginated(MemberPagingParameters parameters)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var baseQuery = from m in context.Members
                                join ms in context.Memberships on m.MemberID equals ms.MemberID
                                join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                where ms.IsActive == true && ms.EndDate > DateTime.Now && ms.IsFrozen==false
                                && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                                && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                                select new
                                {
                                    MemberID = m.MemberID,
                                    MembershipID = ms.MembershipID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    Gender = m.Gender,
                                    Branch = mt.Branch,
                                    RemainingDays = ms.StartDate > DateTime.Now
                                        ? EF.Functions.DateDiffDay(ms.StartDate, ms.EndDate) // Gelecekteki başlangıç için
                                        : EF.Functions.DateDiffDay(DateTime.Now, ms.EndDate), // Başlamış üyelik için
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate,
                                    IsActive = ms.IsActive == true,
                                    UpdatedDate = ms.UpdatedDate, // Üyeliğin güncellenme tarihi
                                    CreationDate = ms.CreationDate, // Üyeliğin oluşturulma tarihi
                                    IsFutureStartDate = ms.StartDate > DateTime.Now // Başlangıç tarihi gelecekte mi
                                };

                // Önce verileri hafızaya alıp gruplandırma yapıyoruz
                var groupedQuery = baseQuery.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.Gender, x.Branch })
                    .Select(g => new MemberFilter
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(x => x.MembershipID), // En son eklenen üyeliğin ID'si
                        Name = g.Key.Name,
                        PhoneNumber = g.Key.PhoneNumber,
                        Gender = g.Key.Gender,
                        Branch = g.Key.Branch,
                        RemainingDays = g.Sum(x => x.RemainingDays), // Kalan günleri topluyoruz
                        StartDate = g.Min(x => x.StartDate), // En erken başlangıç tarihi
                        EndDate = g.Max(x => x.EndDate), // En geç bitiş tarihi
                        IsActive = true,
                        UpdatedDate = g.Max(x => x.UpdatedDate ?? x.CreationDate), // En son güncelleme tarihi veya oluşturma tarihi
                        IsFutureStartDate = g.Any(x => x.IsFutureStartDate) // Herhangi bir üyeliğin başlangıç tarihi gelecekte mi
                    });

                // Filtreleme işlemleri
                var filteredQuery = groupedQuery.AsQueryable();

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    filteredQuery = filteredQuery.Where(x =>
                        x.Name.ToLower().Contains(searchText) ||
                        x.PhoneNumber.Contains(searchText));
                }

                if (parameters.Gender.HasValue)
                {
                    // Gelen int? değerini byte'a cast et
                    byte genderByte = (byte)parameters.Gender.Value;
                    filteredQuery = filteredQuery.Where(x => x.Gender == genderByte);
                }

                if (!string.IsNullOrWhiteSpace(parameters.Branch))
                {
                    filteredQuery = filteredQuery.Where(x => x.Branch == parameters.Branch);
                }

                // Yeni paket bazlı filtreleme
                if (parameters.MembershipTypeID.HasValue)
                {
                    // Belirli bir paket türü seçilmişse
                    var membershipTypeIds = context.MembershipTypes
                        .Where(mt => mt.MembershipTypeID == parameters.MembershipTypeID.Value && mt.CompanyID == companyId)
                        .Select(mt => mt.MembershipTypeID)
                        .ToList();

                    if (membershipTypeIds.Any())
                    {
                        // Bu üyelerin hangi MembershipID'lere sahip olduğunu bul
                        var relevantMembershipIds = context.Memberships
                            .Where(m => membershipTypeIds.Contains(m.MembershipTypeID) &&
                                       m.IsActive == true &&
                                       m.EndDate > DateTime.Now &&
                                       m.CompanyID == companyId)
                            .Select(m => m.MembershipID)
                            .ToList();

                        filteredQuery = filteredQuery.Where(x => relevantMembershipIds.Contains(x.MembershipID));
                    }
                }

                if (!string.IsNullOrWhiteSpace(parameters.PackageGroup))
                {
                    // Paket grubu bazlı filtreleme (örn: "1 Aylık", "3 Aylık")
                    var packageGroupMembershipTypes = context.MembershipTypes
                        .Where(mt => mt.TypeName.Contains(parameters.PackageGroup) && mt.CompanyID == companyId)
                        .Select(mt => mt.MembershipTypeID)
                        .ToList();

                    if (packageGroupMembershipTypes.Any())
                    {
                        var relevantMembershipIds = context.Memberships
                            .Where(m => packageGroupMembershipTypes.Contains(m.MembershipTypeID) &&
                                       m.IsActive == true &&
                                       m.EndDate > DateTime.Now &&
                                       m.CompanyID == companyId)
                            .Select(m => m.MembershipID)
                            .ToList();

                        filteredQuery = filteredQuery.Where(x => relevantMembershipIds.Contains(x.MembershipID));
                    }
                }

                if (parameters.MinDays.HasValue || parameters.MaxDays.HasValue)
                {
                    // Gün aralığı bazlı filtreleme
                    if (parameters.MinDays.HasValue)
                    {
                        filteredQuery = filteredQuery.Where(x => x.RemainingDays >= parameters.MinDays.Value);
                    }
                    if (parameters.MaxDays.HasValue)
                    {
                        filteredQuery = filteredQuery.Where(x => x.RemainingDays <= parameters.MaxDays.Value);
                    }
                }

                // Sıralama - Önce UpdatedDate'e göre, sonra MembershipID'ye göre azalan sıralama
                var orderedQuery = filteredQuery
                    .OrderByDescending(x => x.UpdatedDate)
                    .ThenByDescending(x => x.MembershipID);

                // Sayfalama için toplam kayıt sayısını al
                var totalCount = orderedQuery.Count();

                // Sayfalama uygula
                var items = orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MemberFilter>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
        }

        public List<MembeFilterDto> GetMemberDetails()
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var now = DateTime.Now;

                var query = from m in context.Members
                            join s in context.Memberships
                            on m.MemberID equals s.MemberID
                            join x in context.MembershipTypes
                            on s.MembershipTypeID equals x.MembershipTypeID
                            where s.IsActive == true
                            && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                            && s.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                            && x.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                            select new
                            {
                                m.MemberID,
                                s.MembershipID,
                                x.MembershipTypeID,
                                m.Name,
                                m.Gender,
                                m.PhoneNumber,
                                x.TypeName,
                                x.Branch,
                                s.StartDate,
                                s.EndDate,
                                s.IsActive,
                                m.Balance,
                            };

                var results = query.AsEnumerable();

                var groupedResults = results
                    .GroupBy(r => new { r.MemberID, r.Name, r.Gender, r.PhoneNumber, r.Branch })
                    .Select(g => new MembeFilterDto
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(r => r.MembershipID),
                        MembershipTypeID = g.Max(r => r.MembershipTypeID),
                        Name = g.Key.Name,
                        Gender = g.Key.Gender,
                        PhoneNumber = g.Key.PhoneNumber,
                        Branch = g.Key.Branch,
                        TypeName = string.Join(", ", g.Select(r => r.TypeName).Distinct()),
                        StartDate = g.Min(r => r.StartDate),
                        EndDate = g.Max(r => r.EndDate),
                        RemainingDays = g.Sum(r =>
                            (r.EndDate > now) ?
                            (int)Math.Ceiling((r.EndDate - now).TotalDays) : 0),
                        IsActive = true
                    })
                    .ToList();

                return groupedResults;
            }
        }

        public List<GetActiveMemberDto> GetActiveMembers()
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from m in context.Members
                             where m.IsActive == true
                             && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                             select new GetActiveMemberDto
                             {
                                 MemberID = m.MemberID,
                                 Name = m.Name,
                                 Gender = m.Gender,
                                 PhoneNumber = m.PhoneNumber,
                                 IsActive = m.IsActive,
                                 Adress = m.Adress,
                                 BirthDate = m.BirthDate,
                                 Email = m.Email,
                                 ScanNumber = m.ScanNumber,
                                 Balance = m.Balance,
                             };
                return result.ToList();
            }
        }

        public List<MemberEntryExitHistoryDto> GetMemberEntryExitHistory()
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var currentTime = DateTime.Now;
                var result = from a in context.Members
                             join x in context.Memberships
                             on a.MemberID equals x.MemberID
                             join s in context.EntryExitHistories
                             on x.MembershipID equals s.MembershipID
                             where s.IsActive == true
                             && s.EntryDate.HasValue
                             && EF.Functions.DateDiffMinute(s.EntryDate.Value, currentTime) <= 300
                             && a.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && x.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                             && s.CompanyID == companyId // Giriş-çıkış kayıtlarının da aynı şirkete ait olduğundan emin ol
                             select new MemberEntryExitHistoryDto
                             {
                                 MemberID = a.MemberID,
                                 MemberName = a.Name,
                                 EntryDate = s.EntryDate,
                                 IsActive = s.IsActive,
                             };
                return result.ToList();
            }
        }

        public List<MemberRemainingDayDto> GetMemberRemainingDay()
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var now = DateTime.Now;
                var result = (from a in context.Members
                              join b in context.Memberships on a.MemberID equals b.MemberID
                              join c in context.MembershipTypes on b.MembershipTypeID equals c.MembershipTypeID
                              where b.IsActive == true
                              && a.CompanyID == companyId // Şirket ID'sine göre filtrele
                              && b.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                              && c.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                              select new
                              {
                                  a.MemberID,
                                  a.Name,
                                  a.PhoneNumber,
                                  b.StartDate,
                                  b.EndDate,
                                  c.Branch
                              })
                              .AsEnumerable()
                              .Select(x => new
                              {
                                  MemberID = x.MemberID,
                                  MemberName = x.Name,
                                  PhoneNumber = x.PhoneNumber,
                                  StartDate = x.StartDate,
                                  EndDate = x.EndDate,
                                  RemainingDays = (x.EndDate > now) ? (int)Math.Ceiling((x.EndDate - now).TotalDays) : 0,
                                  Branch = x.Branch
                              })
                              .ToList();

                var groupedResult = result
                    .GroupBy(r => new { r.MemberID, r.Branch })
                    .Select(g => new MemberRemainingDayDto
                    {
                        MemberID = g.Key.MemberID,
                        MemberName = g.First().MemberName,
                        PhoneNumber = g.First().PhoneNumber,
                        StartDate = g.Min(r => r.StartDate),
                        EndDate = g.Max(r => r.EndDate),
                        RemainingDays = g.Sum(r => r.RemainingDays),
                        Message = $"Hoşgeldiniz. {g.First().MemberName.ToUpper()}. {g.Key.Branch} branşında toplam {g.Sum(r => r.RemainingDays)} gün kaldı.",
                        Branch = g.Key.Branch
                    })
                    .Where(dto => dto.RemainingDays > 0 && dto.RemainingDays <= 7)
                    .ToList();

                return groupedResult;
            }
        }

        public Member GetMemberByScanNumber(string scanNumber)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                return context.Members.FirstOrDefault(m => m.ScanNumber == scanNumber && m.CompanyID == companyId && m.IsActive == true);
            }
        }

        public GetMemberQRByPhoneNumberDto GetMemberQRByPhoneNumber(string phoneNumber)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var member = context.Members.FirstOrDefault(m => m.PhoneNumber == phoneNumber && m.CompanyID == companyId && m.IsActive == true);

                if (member == null)
                {
                    return null; // Veya uygun bir hata/boş DTO döndür
                }

                var now = DateTime.Now;

                var allMembershipsQuery = context.Memberships
                    .Where(m => m.MemberID == member.MemberID && m.IsActive == true && m.CompanyID == companyId);

                var allMemberships = allMembershipsQuery.OrderBy(m => m.StartDate).ToList();

                var membershipTypesQuery = context.MembershipTypes.Where(mt => mt.CompanyID == companyId);

                var membershipTypes = membershipTypesQuery.ToDictionary(mt => mt.MembershipTypeID);

                var consolidatedMemberships = new Dictionary<string, MembershipInfo>();

                var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                if (frozenMembership != null)
                {
                    return new GetMemberQRByPhoneNumberDto
                    {
                        Name = member.Name,
                        ScanNumber = member.ScanNumber,
                        IsFrozen = true,
                        FreezeEndDate = frozenMembership.FreezeEndDate,
                        RemainingDays = "Dondurulmuş",
                        Memberships = new List<MembershipInfo>()
                    };
                }

                foreach (var membership in allMemberships)
                {
                    if (!membershipTypes.ContainsKey(membership.MembershipTypeID))
                        continue;

                    var membershipType = membershipTypes[membership.MembershipTypeID];
                    var branch = membershipType.Branch;

                    if (!consolidatedMemberships.ContainsKey(branch))
                    {
                        consolidatedMemberships[branch] = new MembershipInfo
                        {
                            Branch = branch,
                            StartDate = membership.StartDate,
                            EndDate = membership.EndDate,
                            RemainingDays = 0
                        };
                    }

                    var existingMembership = consolidatedMemberships[branch];

                    if (membership.StartDate < existingMembership.StartDate)
                    {
                        existingMembership.StartDate = membership.StartDate;
                    }

                    if (membership.EndDate > existingMembership.EndDate)
                    {
                        existingMembership.EndDate = membership.EndDate;
                    }

                    int remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);
                    existingMembership.RemainingDays += Math.Max(0, remainingDays);
                }

                string message;
                if (consolidatedMemberships.Count == 0 || consolidatedMemberships.All(m => m.Value.EndDate <= now))
                {
                    message = "Üyeliğinizin Süresi Dolmuştur";
                }
                else if (consolidatedMemberships.All(m => m.Value.StartDate > now))
                {
                    var earliestMembership = consolidatedMemberships.Values.OrderBy(m => m.StartDate).First();
                    message = $"üyeliğinizin başlamasına {(int)(earliestMembership.StartDate - DateTime.Today).TotalDays} gün vardır.";
                }
                else
                {
                    message = "üyeliğiniz aktif durumdadır.";
                }

                return new GetMemberQRByPhoneNumberDto
                {
                    Name = member.Name,
                    ScanNumber = member.ScanNumber,
                    IsFrozen = false,
                    RemainingDays = consolidatedMemberships.Count > 0 ? consolidatedMemberships.Values.Sum(m => m.RemainingDays).ToString() : "Süresi Dolmuş",
                    Memberships = consolidatedMemberships.Values.ToList(),
                    Message = message
                };
            }
        }

       public List<MemberEntryDto> GetTodayEntries(DateTime date)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;
                // Önce temel sorguyu oluşturalım
                var baseQuery = from m in context.Members
                                join ms in context.Memberships on m.MemberID equals ms.MemberID
                                join eh in context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                                where eh.EntryDate.HasValue && eh.EntryDate.Value.Date == date.Date
                                && ms.IsActive == true
                                && m.CompanyID == companyId // Şirket ID filtresi eklendi
                                && ms.CompanyID == companyId // Şirket ID filtresi eklendi
                                && eh.CompanyID == companyId // Şirket ID filtresi eklendi
                                select new
                                {
                                    MemberID = m.MemberID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    EntryTime = eh.EntryDate.Value,
                                    ExitTime = eh.ExitDate,
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate
                                };

                // Üye bazında gruplayıp, her üyenin tüm üyeliklerini değerlendirelim
                var result = baseQuery.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.EntryTime, x.ExitTime })
                    .Select(g =>
                    {
                        // Üyeliklerin bitiş tarihlerine göre kalan günleri hesaplayalım
                        var totalRemainingDays = context.Memberships
                            .Where(ms => ms.MemberID == g.Key.MemberID && ms.IsActive == true && ms.CompanyID == companyId) // Şirket ID filtresi eklendi
                            .AsEnumerable()
                            .Where(ms => ms.EndDate > now) // Sadece aktif üyelikler
                            .Sum(ms =>
                            {
                                if (ms.StartDate > now)
                                {
                                    return 0; // Henüz başlamamış üyelikler için 0 gün
                                }
                                return (int)Math.Ceiling((ms.EndDate - now).TotalDays);
                            });

                        return new MemberEntryDto
                        {
                            MemberID = g.Key.MemberID,
                            Name = g.Key.Name,
                            PhoneNumber = g.Key.PhoneNumber,
                            EntryTime = g.Key.EntryTime,
                            ExitTime = g.Key.ExitTime,
                            RemainingDays = totalRemainingDays
                        };
                    })
                    .OrderByDescending(x => x.EntryTime)
                    .ToList();

                return result;
            }
        }
        public List<MemberEntryDto> GetMemberEntriesByName(string searchText)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from m in context.Members
                             join ms in context.Memberships on m.MemberID equals ms.MemberID
                             join eh in context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                             where (m.Name.Contains(searchText) || m.PhoneNumber.Contains(searchText))
                                   && eh.EntryDate.HasValue
                                   && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                   && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                                   && eh.CompanyID == companyId // Giriş-çıkış kayıtlarının da aynı şirkete ait olduğundan emin ol
                             orderby eh.EntryDate descending // Tarihe göre tersten sıralama
                             select new MemberEntryDto
                             {
                                 MemberID = m.MemberID,
                                 Name = m.Name,
                                 PhoneNumber = m.PhoneNumber,
                                 EntryTime = eh.EntryDate.Value,
                                 ExitTime = eh.ExitDate
                             };

                return result.ToList();
            }
        }

        /// <summary>
        /// Branş bazlı paket filtreleme bilgilerini getirir
        /// </summary>
        public List<BranchPackageFilterDto> GetBranchPackageFilters()
        {
            using (GymContext context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;

                // Aktif üyelikleri al
                var activeMemberships = from m in context.Members
                                       join ms in context.Memberships on m.MemberID equals ms.MemberID
                                       join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                       where ms.IsActive == true &&
                                             ms.EndDate > now &&
                                             ms.IsFrozen == false &&
                                             m.CompanyID == companyId &&
                                             ms.CompanyID == companyId &&
                                             mt.CompanyID == companyId
                                       select new
                                       {
                                           MemberID = m.MemberID,
                                           Branch = mt.Branch,
                                           MembershipTypeID = mt.MembershipTypeID,
                                           TypeName = mt.TypeName,
                                           Day = mt.Day
                                       };

                // Branş bazlı gruplama
                var branchGroups = activeMemberships.AsEnumerable()
                    .GroupBy(x => x.Branch)
                    .Select(branchGroup => new BranchPackageFilterDto
                    {
                        Branch = branchGroup.Key,
                        TotalMembers = branchGroup.Select(x => x.MemberID).Distinct().Count(),
                        Packages = branchGroup
                            .GroupBy(x => new { x.MembershipTypeID, x.TypeName, x.Day })
                            .Select(packageGroup => new PackageFilterDto
                            {
                                MembershipTypeID = packageGroup.Key.MembershipTypeID,
                                TypeName = packageGroup.Key.TypeName,
                                Day = packageGroup.Key.Day,
                                MemberCount = packageGroup.Select(x => x.MemberID).Distinct().Count(),
                                PackageGroup = GetPackageGroup(packageGroup.Key.Day)
                            })
                            .OrderBy(p => p.Day)
                            .ToList()
                    })
                    .OrderBy(b => b.Branch)
                    .ToList();

                return branchGroups;
            }
        }

        /// <summary>
        /// Çoklu üyelik görünümü için üye detaylarını getirir
        /// </summary>
        public List<MemberMultipleMembershipDto> GetMembersWithMultipleMemberships(string branch = null)
        {
            using (GymContext context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;

                var query = from m in context.Members
                           join ms in context.Memberships on m.MemberID equals ms.MemberID
                           join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                           join p in context.Payments on ms.MembershipID equals p.MemberShipID into payments
                           from payment in payments.DefaultIfEmpty()
                           where ms.IsActive == true &&
                                 ms.EndDate > now &&
                                 m.CompanyID == companyId &&
                                 ms.CompanyID == companyId &&
                                 mt.CompanyID == companyId
                           select new
                           {
                               MemberID = m.MemberID,
                               MemberName = m.Name,
                               PhoneNumber = m.PhoneNumber,
                               Gender = m.Gender,
                               MembershipID = ms.MembershipID,
                               MembershipTypeID = mt.MembershipTypeID,
                               Branch = mt.Branch,
                               TypeName = mt.TypeName,
                               Day = mt.Day,
                               StartDate = ms.StartDate,
                               EndDate = ms.EndDate,
                               IsFrozen = ms.IsFrozen,
                               Price = payment != null ? payment.PaymentAmount : 0,
                               UpdatedDate = ms.UpdatedDate ?? ms.CreationDate
                           };

                if (!string.IsNullOrWhiteSpace(branch))
                {
                    query = query.Where(x => x.Branch == branch);
                }

                var result = query.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.MemberName, x.PhoneNumber, x.Gender })
                    .Select(memberGroup => new MemberMultipleMembershipDto
                    {
                        MemberID = memberGroup.Key.MemberID,
                        Name = memberGroup.Key.MemberName,
                        PhoneNumber = memberGroup.Key.PhoneNumber,
                        Gender = memberGroup.Key.Gender,
                        ActiveMemberships = memberGroup
                            .Select(x => new MembershipDetailViewDto
                            {
                                MembershipID = x.MembershipID,
                                MembershipTypeID = x.MembershipTypeID,
                                Branch = x.Branch,
                                TypeName = x.TypeName,
                                Day = x.Day,
                                StartDate = x.StartDate,
                                EndDate = x.EndDate,
                                RemainingDays = Math.Max(0, (int)Math.Ceiling((x.EndDate - now).TotalDays)),
                                IsFrozen = x.IsFrozen,
                                Price = x.Price
                            })
                            .OrderBy(ms => ms.Branch)
                            .ThenBy(ms => ms.EndDate)
                            .ToList(),
                        TotalRemainingDays = memberGroup.Sum(x => Math.Max(0, (int)Math.Ceiling((x.EndDate - now).TotalDays))),
                        LastUpdateDate = memberGroup.Max(x => x.UpdatedDate)
                    })
                    .OrderByDescending(x => x.LastUpdateDate)
                    .ToList();

                return result;
            }
        }

        /// <summary>
        /// Üyenin tüm aktif üyeliklerini güvenli silme için listeler
        /// </summary>
        public MemberActiveMembershipsDto GetMemberActiveMembershipsForDelete(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;

                // Üye bilgilerini al
                var member = context.Members.FirstOrDefault(m =>
                    m.MemberID == memberId &&
                    m.IsActive == true &&
                    m.CompanyID == companyId);

                if (member == null)
                    return null;

                // Aktif üyelikleri al
                var activeMemberships = from ms in context.Memberships
                                       join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                       join payments in context.Payments on ms.MembershipID equals payments.MemberShipID into paymentGroup
                                       where ms.MemberID == memberId &&
                                             ms.IsActive == true &&
                                             ms.EndDate > now &&
                                             ms.CompanyID == companyId &&
                                             mt.CompanyID == companyId
                                       select new
                                       {
                                           Membership = ms,
                                           MembershipType = mt,
                                           Payments = paymentGroup.Where(p => p.IsActive == true).ToList()
                                       };

                var membershipList = activeMemberships.ToList();

                var membershipOptions = membershipList.Select(item =>
                {
                    var remainingDays = Math.Max(0, (int)Math.Ceiling((item.Membership.EndDate - now).TotalDays));
                    var totalPaid = item.Payments.Sum(p => p.PaymentAmount);
                    var paymentMethods = string.Join(", ", item.Payments.Select(p => p.PaymentMethod).Distinct());

                    // Silme uyarısı oluştur
                    string deleteWarning = "";
                    bool canBeDeleted = true;

                    if (item.Membership.IsFrozen)
                    {
                        deleteWarning = "Bu üyelik dondurulmuş durumda. Silme işlemi dondurmayı iptal edecek.";
                    }

                    if (remainingDays > 30)
                    {
                        deleteWarning += $" {remainingDays} gün kalan süre kaybolacak.";
                    }

                    if (totalPaid > 0)
                    {
                        deleteWarning += $" {totalPaid:C} ödeme tutarı kaybolacak.";
                    }

                    return new MembershipDeleteOptionDto
                    {
                        MembershipID = item.Membership.MembershipID,
                        MembershipTypeID = item.MembershipType.MembershipTypeID,
                        Branch = item.MembershipType.Branch,
                        TypeName = item.MembershipType.TypeName,
                        Day = item.MembershipType.Day,
                        StartDate = item.Membership.StartDate,
                        EndDate = item.Membership.EndDate,
                        RemainingDays = remainingDays,
                        IsFrozen = item.Membership.IsFrozen,
                        FreezeEndDate = item.Membership.FreezeEndDate,
                        TotalPaidAmount = totalPaid,
                        PaymentCount = item.Payments.Count,
                        LastPaymentDate = item.Payments.Any() ? item.Payments.Max(p => p.PaymentDate) : DateTime.MinValue,
                        PaymentMethods = paymentMethods,
                        CanBeDeleted = canBeDeleted,
                        DeleteWarning = deleteWarning.Trim()
                    };
                }).ToList();

                return new MemberActiveMembershipsDto
                {
                    MemberID = member.MemberID,
                    MemberName = member.Name,
                    PhoneNumber = member.PhoneNumber,
                    Gender = member.Gender,
                    ActiveMemberships = membershipOptions,
                    TotalActiveMemberships = membershipOptions.Count,
                    TotalPaidAmount = membershipOptions.Sum(m => m.TotalPaidAmount),
                    HasMultipleMemberships = membershipOptions.Count > 1
                };
            }
        }

        /// <summary>
        /// Gün sayısına göre paket grubu belirler
        /// </summary>
        private string GetPackageGroup(int days)
        {
            return days switch
            {
                <= 31 => "1 Aylık",
                <= 62 => "2 Aylık",
                <= 93 => "3 Aylık",
                <= 124 => "4 Aylık",
                <= 155 => "5 Aylık",
                <= 186 => "6 Aylık",
                <= 365 => "Yıllık",
                _ => "Özel Paket"
            };
        }
    } // EfMemberDal sınıfı kapanışı
} // namespace kapanışı
