﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Business;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Transactions;

namespace Business.Concrete
{
    public class MembershipManager : IMembershipService
    {
        IMembershipDal _membershipDal;
        IPaymentDal _paymentDal;
        IRemainingDebtDal _remainingDebtDal;
        IMembershipFreezeHistoryService _freezeHistoryService;

        public MembershipManager(IMembershipDal membershipDal,IPaymentDal paymentDal,IRemainingDebtDal remainingDebtDal, IMembershipFreezeHistoryService freezeHistoryService)
        {
            _membershipDal = membershipDal;
            _paymentDal = paymentDal;
            _remainingDebtDal = remainingDebtDal;
            _freezeHistoryService = freezeHistoryService;

        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult CancelFreeze(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult("Üyelik dondurulmamış durumda.");

            var membership = _membershipDal.Get(m => m.MembershipID == membershipId);
            if (membership == null)
                return new ErrorResult("Üyelik bulunamadı.");

            // En son dondurma kaydını bul ve güncelle
            var freezeHistories = _freezeHistoryService.GetByMembershipId(membershipId).Data;
            var lastFreezeHistory = freezeHistories.OrderByDescending(x => x.CreationDate).FirstOrDefault();

            if (lastFreezeHistory != null)
            {
                var history = new MembershipFreezeHistory
                {
                    FreezeHistoryID = lastFreezeHistory.FreezeHistoryID,
                    MembershipID = membershipId,
                    StartDate = lastFreezeHistory.StartDate,
                    PlannedEndDate = lastFreezeHistory.PlannedEndDate,
                    ActualEndDate = DateTime.Now,
                    FreezeDays = lastFreezeHistory.FreezeDays,
                    UsedDays = 0, // Tamamen iptal edildiği için kullanılan gün 0
                    CancellationType = "Tamamen İptal",
                    CreationDate = lastFreezeHistory.CreationDate
                };

                _freezeHistoryService.Update(history);
            }

            _membershipDal.CancelFreeze(membershipId);
            return new SuccessResult("Üyelik dondurma işlemi tamamen iptal edildi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult ReactivateFromToday(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult("Üyelik dondurulmamış durumda.");

            var membership = _membershipDal.Get(m => m.MembershipID == membershipId);
            if (membership == null)
                return new ErrorResult("Üyelik bulunamadı.");

            // En son dondurma kaydını bul ve güncelle
            var freezeHistories = _freezeHistoryService.GetByMembershipId(membershipId).Data;
            var lastFreezeHistory = freezeHistories.OrderByDescending(x => x.CreationDate).FirstOrDefault();

            if (lastFreezeHistory != null)
            {
                var usedDays = (int)(DateTime.Now - lastFreezeHistory.StartDate).TotalDays;

                var history = new MembershipFreezeHistory
                {
                    FreezeHistoryID = lastFreezeHistory.FreezeHistoryID,
                    MembershipID = membershipId,
                    StartDate = lastFreezeHistory.StartDate,
                    PlannedEndDate = lastFreezeHistory.PlannedEndDate,
                    ActualEndDate = DateTime.Now,
                    FreezeDays = lastFreezeHistory.FreezeDays,
                    UsedDays = usedDays,
                    CancellationType = "Erken Başlatma",
                    CreationDate = lastFreezeHistory.CreationDate
                };

                _freezeHistoryService.Update(history);
            }

            _membershipDal.ReactivateFromToday(membershipId);
            return new SuccessResult("Üyelik bugünden itibaren aktif edildi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult FreezeMembership(MembershipFreezeRequestDto freezeRequest)
        {
            if (freezeRequest.FreezeDays < 1 || freezeRequest.FreezeDays > 365)
                return new ErrorResult(Messages.FreezeDaysInvalid);

            var remainingDaysResult = _freezeHistoryService.GetRemainingFreezeDays(freezeRequest.MembershipID);
            if (!remainingDaysResult.Success || remainingDaysResult.Data < freezeRequest.FreezeDays)
                return new ErrorResult("Yıllık dondurma hakkınız yetersiz");

            if (_membershipDal.IsMembershipFrozen(freezeRequest.MembershipID))
                return new ErrorResult(Messages.MembershipAlreadyFrozen);

            var freezeStartDate = DateTime.Now;
            // Bitiş tarihini gün olarak ayarla ve saati 00:01 olarak belirle
            var freezeEndDate = freezeStartDate.AddDays(freezeRequest.FreezeDays)
                .Date
                .AddHours(0)
                .AddMinutes(1);

            // Üyeliği dondur
            _membershipDal.FreezeMembership(freezeRequest.MembershipID, freezeRequest.FreezeDays);

            // Dondurma geçmişine kaydet
            var freezeHistory = new MembershipFreezeHistory
            {
                MembershipID = freezeRequest.MembershipID,
                StartDate = freezeStartDate,
                PlannedEndDate = freezeEndDate,
                FreezeDays = freezeRequest.FreezeDays,
                CreationDate = DateTime.Now
            };

            _freezeHistoryService.Add(freezeHistory);

            return new SuccessResult(Messages.MembershipFrozen);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult UnfreezeMembership(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult(Messages.MembershipAlreadyFrozen);

            _membershipDal.UnfreezeMembership(membershipId);
            return new SuccessResult(Messages.MembershipUnfrozen);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Membership", "Frozen")]
        public IDataResult<List<MembershipFreezeDto>> GetFrozenMemberships()
        {
            return new SuccessDataResult<List<MembershipFreezeDto>>(_membershipDal.GetFrozenMemberships());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult Add(MembershipAddDto membershipDto)
        {
            // Aynı branş + aynı paket kontrolü (mevcut mantık)
            var existingMembership = _membershipDal.Get(m =>
                m.MemberID == membershipDto.MemberID &&
                m.MembershipTypeID == membershipDto.MembershipTypeID &&
                m.EndDate >= DateTime.Now &&
                m.IsActive == true
            );

            // Aynı branş + farklı paket kontrolü (yeni mantık)
            var newMembershipType = _membershipDal.GetMembershipType(membershipDto.MembershipTypeID);
            var existingDifferentPackage = _membershipDal.GetAll(m =>
                m.MemberID == membershipDto.MemberID &&
                m.MembershipTypeID != membershipDto.MembershipTypeID &&
                m.EndDate >= DateTime.Now &&
                m.IsActive == true
            ).Where(m => {
                var existingType = _membershipDal.GetMembershipType(m.MembershipTypeID);
                return existingType != null && existingType.Branch == newMembershipType.Branch;
            }).OrderByDescending(m => m.EndDate).FirstOrDefault();

            string paymentStatus = membershipDto.PaymentMethod== "Borç" ? "Pending" : "Completed";

            // SENARYO 1: Aynı branş + aynı paket (mevcut mantık - EndDate uzat)
            if (existingMembership != null)
            {
                using (var scope = new TransactionScope())
                {
                    try
                    {
                        existingMembership.EndDate = existingMembership.EndDate.AddDays(membershipDto.Day);
                        existingMembership.UpdatedDate = DateTime.Now;
                        _membershipDal.Update(existingMembership);

                        Payment payment = new Payment
                        {
                            PaymentAmount = membershipDto.Price,
                            PaymentMethod = membershipDto.PaymentMethod,
                            OriginalPaymentMethod = membershipDto.PaymentMethod,
                            FinalPaymentMethod = membershipDto.PaymentMethod,
                            PaymentDate = DateTime.Now,
                            MemberShipID = existingMembership.MembershipID,
                            PaymentStatus = paymentStatus,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        _paymentDal.Add(payment);

                        if (membershipDto.PaymentMethod == "Borç")
                        {
                            RemainingDebt remainingDebt = new RemainingDebt
                            {
                                PaymentID = payment.PaymentID,
                                OriginalAmount = membershipDto.Price,
                                RemainingAmount = membershipDto.Price,
                                LastUpdateDate = DateTime.Now,
                                IsActive = true,
                                CreationDate = DateTime.Now
                            };
                            _remainingDebtDal.Add(remainingDebt);
                        }

                        scope.Complete();
                        return new SuccessResult(Messages.MembershipUpdated);
                    }
                    catch
                    {
                        scope.Dispose();
                        return new ErrorResult(Messages.MembershipNotFound);
                    }
                }
            }
            // SENARYO 2: Aynı branş + farklı paket (yeni mantık - yeni kayıt + günleri birleştir)
            else if (existingDifferentPackage != null)
            {
                using (var scope = new TransactionScope())
                {
                    try
                    {
                        // Kalan günleri hesapla
                        var remainingDays = Math.Max(0, (int)Math.Ceiling((existingDifferentPackage.EndDate - DateTime.Now).TotalDays));

                        // Yeni üyelik oluştur
                        Membership newMembership = new Membership
                        {
                            MemberID = membershipDto.MemberID,
                            MembershipTypeID = membershipDto.MembershipTypeID,
                            StartDate = membershipDto.StartDate,
                            EndDate = membershipDto.EndDate.AddDays(remainingDays), // Kalan günleri ekle
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        _membershipDal.Add(newMembership);

                        // Eski üyeliği pasif yap
                        existingDifferentPackage.IsActive = false;
                        existingDifferentPackage.UpdatedDate = DateTime.Now;
                        _membershipDal.Update(existingDifferentPackage);

                        Payment payment = new Payment
                        {
                            PaymentAmount = membershipDto.Price,
                            PaymentMethod = membershipDto.PaymentMethod,
                            OriginalPaymentMethod = membershipDto.PaymentMethod,
                            FinalPaymentMethod = membershipDto.PaymentMethod,
                            PaymentDate = DateTime.Now,
                            MemberShipID = newMembership.MembershipID,
                            PaymentStatus = paymentStatus,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        _paymentDal.Add(payment);

                        if (membershipDto.PaymentMethod == "Borç")
                        {
                            RemainingDebt remainingDebt = new RemainingDebt
                            {
                                PaymentID = payment.PaymentID,
                                OriginalAmount = membershipDto.Price,
                                RemainingAmount = membershipDto.Price,
                                LastUpdateDate = DateTime.Now,
                                IsActive = true,
                                CreationDate = DateTime.Now
                            };
                            _remainingDebtDal.Add(remainingDebt);
                        }

                        scope.Complete();
                        return new SuccessResult($"Üyelik başarıyla güncellendi. Eski paketten {remainingDays} gün yeni pakete aktarıldı.");
                    }
                    catch
                    {
                        scope.Dispose();
                        return new ErrorResult(Messages.MembershipNotFound);
                    }
                }
            }
            // SENARYO 3: Farklı branş veya ilk üyelik (mevcut mantık - yeni kayıt oluştur)
            else
            {
                using (var scope = new TransactionScope())
                {
                    try
                    {
                        Membership newMembership = new Membership
                        {
                            MemberID = membershipDto.MemberID,
                            MembershipTypeID = membershipDto.MembershipTypeID,
                            StartDate = membershipDto.StartDate,
                            EndDate = membershipDto.EndDate,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        _membershipDal.Add(newMembership);

                        Payment payment = new Payment
                        {
                            PaymentAmount = membershipDto.Price,
                            PaymentMethod = membershipDto.PaymentMethod,
                            OriginalPaymentMethod = membershipDto.PaymentMethod,
                            FinalPaymentMethod = membershipDto.PaymentMethod,
                            PaymentDate = DateTime.Now,
                            MemberShipID = newMembership.MembershipID,
                            PaymentStatus = paymentStatus,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        _paymentDal.Add(payment);

                        if (membershipDto.PaymentMethod == "Borç")
                        {
                            RemainingDebt remainingDebt = new RemainingDebt
                            {
                                PaymentID = payment.PaymentID,
                                OriginalAmount = membershipDto.Price,
                                RemainingAmount = membershipDto.Price,
                                LastUpdateDate = DateTime.Now,
                                IsActive = true,
                                CreationDate = DateTime.Now
                            };
                            _remainingDebtDal.Add(remainingDebt);
                        }

                        scope.Complete();
                        return new SuccessResult(Messages.MembershipAdded);
                    }
                    catch
                    {
                        scope.Dispose();
                        return new ErrorResult(Messages.MembershipNotFound);
                    }
                }
            }
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult Update(MembershipUpdateDto membershipDto)
        {
            var membership = _membershipDal.Get(m => m.MembershipID == membershipDto.MembershipID);

            if (membership == null)
            {
                return new ErrorResult(Messages.MembershipNotFound);
            }

            membership.MembershipTypeID = membershipDto.MembershipTypeID;
            membership.StartDate = membershipDto.StartDate;
            membership.EndDate = membershipDto.EndDate;
            membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle

            _membershipDal.Update(membership);
            return new SuccessResult(Messages.MembershipUpdated);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult Delete(int id)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    var membership = _membershipDal.Get(m => m.MembershipID == id);
                    if (membership == null)
                    {
                        return new ErrorResult(Messages.MembershipNotFound);
                    }

                    var payments = _paymentDal.GetAll(p => p.MemberShipID == id);
                    foreach (var payment in payments)
                    {
                        // Ödemeye ait borç kaydını bul ve sil
                        var remainingDebt = _remainingDebtDal.Get(rd => rd.PaymentID == payment.PaymentID);
                        if (remainingDebt != null)
                        {
                            remainingDebt.IsActive = false;
                            _remainingDebtDal.Update(remainingDebt);
                        }

                        _paymentDal.Delete(payment.PaymentID);
                    }

                    _membershipDal.Delete(id);

                    scope.Complete();
                    return new SuccessResult(Messages.MembershipDeleted);
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Üyelik silinirken bir hata oluştu: {ex.Message}");
                }
            }
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Membership", "All")]
        public IDataResult<List<Membership>> GetAll()
        {
            return new SuccessDataResult<List<Membership>>(_membershipDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        public IDataResult<List<Membership>> GetByMembershipId(int memberid)
        {
            return new SuccessDataResult<List<Membership>>(_membershipDal.GetAll(c => c.MembershipID == memberid));
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Membership", "LastInfo")]
        public IDataResult<LastMembershipInfoDto> GetLastMembershipInfo(int memberId)
        {
            var lastMembership = _membershipDal.GetAll(m => m.MemberID == memberId && m.DeletedDate == null)
                                              .OrderByDescending(m => m.EndDate)
                                              .FirstOrDefault();
            if (lastMembership != null)
            {
                var now = DateTime.Now;
                var daysRemaining = Math.Ceiling((lastMembership.EndDate - now).TotalDays);
                var isActive = daysRemaining > 0;
                return new SuccessDataResult<LastMembershipInfoDto>(new LastMembershipInfoDto
                {
                    LastEndDate = lastMembership.EndDate,
                    DaysRemaining = (int)daysRemaining,
                    IsActive = isActive
                });
            }
            return new SuccessDataResult<LastMembershipInfoDto>(new LastMembershipInfoDto
            {
                LastEndDate = null,
                DaysRemaining = 0,
                IsActive = false
            });
        }

        /// <summary>
        /// Çoklu üyelik güvenli silme işlemi
        /// </summary>
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IDataResult<MembershipDeleteResultDto> DeleteMultipleMemberships(MembershipDeleteRequestDto deleteRequest)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    var result = new MembershipDeleteResultDto
                    {
                        Success = false,
                        DeletedMembershipsCount = 0,
                        RefundableAmount = 0,
                        DeletedMembershipDetails = new List<string>(),
                        Warnings = new List<string>()
                    };

                    // Silinecek üyelikleri belirle
                    List<int> membershipIdsToDelete;
                    if (deleteRequest.DeleteAllMemberships)
                    {
                        // Üyenin tüm aktif üyeliklerini al
                        var allActiveMemberships = _membershipDal.GetAll(m =>
                            m.MemberID == deleteRequest.MemberID &&
                            m.IsActive == true &&
                            m.EndDate > DateTime.Now);
                        membershipIdsToDelete = allActiveMemberships.Select(m => m.MembershipID).ToList();
                    }
                    else
                    {
                        membershipIdsToDelete = deleteRequest.MembershipIDsToDelete;
                    }

                    if (!membershipIdsToDelete.Any())
                    {
                        return new ErrorDataResult<MembershipDeleteResultDto>(result, "Silinecek üyelik bulunamadı.");
                    }

                    // Her üyeliği sil
                    foreach (var membershipId in membershipIdsToDelete)
                    {
                        var membership = _membershipDal.Get(m => m.MembershipID == membershipId);
                        if (membership == null) continue;

                        // Üyelik türü bilgisini al
                        var membershipType = _membershipDal.GetMembershipType(membership.MembershipTypeID);

                        // Ödeme bilgilerini al
                        var payments = _paymentDal.GetAll(p => p.MemberShipID == membershipId && p.IsActive == true);
                        var totalPaidAmount = payments.Sum(p => p.PaymentAmount);

                        // Dondurulmuş üyelik kontrolü
                        if (membership.IsFrozen)
                        {
                            result.Warnings.Add($"{membershipType?.Branch} - {membershipType?.TypeName}: Dondurulmuş üyelik silindi.");
                        }

                        // Kalan gün kontrolü
                        var remainingDays = Math.Max(0, (int)Math.Ceiling((membership.EndDate - DateTime.Now).TotalDays));
                        if (remainingDays > 0)
                        {
                            result.Warnings.Add($"{membershipType?.Branch} - {membershipType?.TypeName}: {remainingDays} gün kalan süre silindi.");
                        }

                        // Ödemeleri sil
                        foreach (var payment in payments)
                        {
                            // Borç kaydını pasif yap
                            var remainingDebt = _remainingDebtDal.Get(rd => rd.PaymentID == payment.PaymentID);
                            if (remainingDebt != null)
                            {
                                remainingDebt.IsActive = false;
                                remainingDebt.LastUpdateDate = DateTime.Now;
                                _remainingDebtDal.Update(remainingDebt);
                            }

                            // Ödemeyi sil
                            _paymentDal.Delete(payment.PaymentID);
                        }

                        // Üyeliği sil
                        _membershipDal.Delete(membershipId);

                        // Sonuç bilgilerini güncelle
                        result.DeletedMembershipsCount++;
                        result.RefundableAmount += totalPaidAmount;
                        result.DeletedMembershipDetails.Add(
                            $"{membershipType?.Branch} - {membershipType?.TypeName} ({remainingDays} gün kalan, {totalPaidAmount:C} ödeme)"
                        );
                    }

                    // Yönetici onayı gerekli mi kontrol et
                    if (result.RefundableAmount > 1000) // 1000 TL üzeri için yönetici onayı
                    {
                        result.RequiresManagerApproval = true;
                        result.Warnings.Add("Yüksek tutarlı silme işlemi için yönetici onayı gerekebilir.");
                    }

                    scope.Complete();

                    result.Success = true;
                    result.Message = $"{result.DeletedMembershipsCount} üyelik başarıyla silindi.";

                    return new SuccessDataResult<MembershipDeleteResultDto>(result);
                }
                catch (Exception ex)
                {
                    scope.Dispose();
                    return new ErrorDataResult<MembershipDeleteResultDto>(
                        new MembershipDeleteResultDto { Success = false },
                        $"Üyelik silme işlemi sırasında hata oluştu: {ex.Message}"
                    );
                }
            }
        }
    }
}
