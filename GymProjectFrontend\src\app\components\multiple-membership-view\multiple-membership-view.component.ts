import { Component, Input, OnInit } from '@angular/core';
import { MemberMultipleMembership, MembershipDetailView } from '../../models/branch-package-filter';
import { faCalendarAlt, faDumbbell, faSnowflake, faClock, faMoneyBillWave } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-multiple-membership-view',
  templateUrl: './multiple-membership-view.component.html',
  styleUrls: ['./multiple-membership-view.component.scss'],
  standalone: false
})
export class MultipleMembershipViewComponent implements OnInit {

  @Input() memberData: MemberMultipleMembership | null = null;
  @Input() showCompactView: boolean = false;
  @Input() showActions: boolean = true;

  // Icons
  faCalendarAlt = faCalendarAlt;
  faDumbbell = faDumbbell;
  faSnowflake = faSnowflake;
  faClock = faClock;
  faMoneyBillWave = faMoneyBillWave;

  constructor() { }

  ngOnInit(): void {
  }

  /**
   * <PERSON><PERSON> gün rengini belirler
   */
  getRemainingDaysClass(days: number): string {
    if (days <= 3) return 'text-danger';
    if (days <= 10) return 'text-warning';
    return 'text-success';
  }

  /**
   * Branş rengini belirler
   */
  getBranchBadgeClass(branch: string): string {
    const branchColors: { [key: string]: string } = {
      'Fitness': 'modern-badge-primary',
      'Crossfit': 'modern-badge-danger',
      'Pilates': 'modern-badge-success',
      'Yoga': 'modern-badge-info',
      'Kickbox': 'modern-badge-warning',
      'Zumba': 'modern-badge-secondary'
    };
    
    return branchColors[branch] || 'modern-badge-dark';
  }

  /**
   * Üyelik durumunu belirler
   */
  getMembershipStatus(membership: MembershipDetailView): string {
    if (membership.isFrozen) return 'Dondurulmuş';
    if (membership.remainingDays <= 0) return 'Süresi Dolmuş';
    if (membership.remainingDays <= 3) return 'Süresi Bitiyor';
    if (membership.remainingDays <= 10) return 'Yakında Bitiyor';
    return 'Aktif';
  }

  /**
   * Üyelik durum rengini belirler
   */
  getMembershipStatusClass(membership: MembershipDetailView): string {
    if (membership.isFrozen) return 'text-info';
    if (membership.remainingDays <= 0) return 'text-danger';
    if (membership.remainingDays <= 3) return 'text-danger';
    if (membership.remainingDays <= 10) return 'text-warning';
    return 'text-success';
  }

  /**
   * Para formatı
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  }

  /**
   * Tarih formatı
   */
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('tr-TR');
  }

  /**
   * En yakın bitiş tarihini bulur
   */
  getClosestEndDate(): string | null {
    if (!this.memberData?.activeMemberships.length) return null;
    
    const closestMembership = this.memberData.activeMemberships
      .filter(m => !m.isFrozen && m.remainingDays > 0)
      .sort((a, b) => new Date(a.endDate).getTime() - new Date(b.endDate).getTime())[0];
    
    return closestMembership ? closestMembership.endDate : null;
  }

  /**
   * Aktif branşları gruplar
   */
  getActiveBranches(): { branch: string, count: number, totalDays: number }[] {
    if (!this.memberData?.activeMemberships.length) return [];
    
    const branchMap = new Map<string, { count: number, totalDays: number }>();
    
    this.memberData.activeMemberships
      .filter(m => !m.isFrozen && m.remainingDays > 0)
      .forEach(membership => {
        const current = branchMap.get(membership.branch) || { count: 0, totalDays: 0 };
        branchMap.set(membership.branch, {
          count: current.count + 1,
          totalDays: current.totalDays + membership.remainingDays
        });
      });
    
    return Array.from(branchMap.entries()).map(([branch, data]) => ({
      branch,
      count: data.count,
      totalDays: data.totalDays
    }));
  }

  /**
   * Toplam ödenen tutarı hesaplar
   */
  getTotalPaidAmount(): number {
    if (!this.memberData?.activeMemberships.length) return 0;
    
    return this.memberData.activeMemberships.reduce((total, membership) => {
      return total + membership.price;
    }, 0);
  }

  /**
   * Üyelikleri branş ve bitiş tarihine göre sıralar
   */
  getSortedMemberships(): MembershipDetailView[] {
    if (!this.memberData?.activeMemberships.length) return [];

    return [...this.memberData.activeMemberships].sort((a, b) => {
      // Önce branşa göre sırala
      if (a.branch !== b.branch) {
        return a.branch.localeCompare(b.branch);
      }
      // Sonra bitiş tarihine göre sırala
      return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();
    });
  }

  /**
   * Compact view için özet bilgi
   */
  getCompactSummary(): string {
    if (!this.memberData?.activeMemberships.length) return 'Aktif üyelik yok';

    const activeBranches = this.getActiveBranches();
    if (activeBranches.length === 1) {
      const branch = activeBranches[0];
      return `${branch.branch} - ${branch.totalDays} gün`;
    } else {
      return `${activeBranches.length} branş - ${this.memberData.totalRemainingDays} gün`;
    }
  }
}
