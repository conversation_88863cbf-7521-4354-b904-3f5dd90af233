<div class="modern-dialog zoom-in">
  <div class="modern-card-header">
    <h2>{{ data.title }}</h2>
    <button class="modern-btn-icon" (click)="dialogRef.close(false)">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="modern-card-body">
    <div class="dialog-icon">
      <i class="fas fa-money-bill-wave"></i>
    </div>

    <p class="dialog-message">{{ data.message }}</p>

    <div class="modern-form-group" *ngIf="data.paymentMethods && data.paymentMethods.length > 0">
      <label class="modern-form-label">Ödeme Yöntemi</label>
      <select class="modern-form-control" [(ngModel)]="selectedPaymentMethod">
        <option [ngValue]="undefined" disabled selected>Ödeme yöntemi seçin</option>
        <option *ngFor="let method of data.paymentMethods" [value]="method">
          {{method}}
        </option>
      </select>
    </div>
  </div>

  <div class="modern-card-footer">
    <button class="modern-btn modern-btn-outline-secondary" (click)="dialogRef.close(false)">
      <i class="fas fa-times modern-btn-icon"></i> İptal
    </button>
    <button
      class="modern-btn modern-btn-success"
      [disabled]="data.paymentMethods && data.paymentMethods.length > 0 && !selectedPaymentMethod"
      (click)="dialogRef.close({ confirmed: true, paymentMethod: selectedPaymentMethod })">
      <i class="fas fa-check modern-btn-icon"></i> Onayla
    </button>
  </div>
</div>
