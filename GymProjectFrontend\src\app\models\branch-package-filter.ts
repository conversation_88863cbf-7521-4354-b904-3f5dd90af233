// İki seviyeli filtreleme için branş ve paket bilgileri
export interface BranchPackageFilter {
  branch: string;
  totalMembers: number;
  packages: PackageFilter[];
}

// Paket bazlı filtreleme bilgileri
export interface PackageFilter {
  membershipTypeID: number;
  typeName: string;
  day: number;
  memberCount: number;
  packageGroup: string; // "1 Aylık", "3 Aylık" vs
}

// Gelişmiş üye filtreleme parametreleri
export interface AdvancedMemberFilter {
  pageNumber?: number;
  pageSize?: number;
  searchText?: string;
  gender?: number;
  branch?: string;
  membershipTypeID?: number;
  packageGroup?: string;
  minDays?: number;
  maxDays?: number;
}

// Çoklu üyelik görünümü için detaylı üye bilgisi
export interface MemberMultipleMembership {
  memberID: number;
  name: string;
  phoneNumber: string;
  gender: number;
  activeMemberships: MembershipDetailView[];
  totalRemainingDays: number;
  lastUpdateDate?: string;
}

// Üyelik detay görünümü
export interface MembershipDetailView {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  day: number;
  startDate: string;
  endDate: string;
  remainingDays: number;
  isFrozen: boolean;
  price: number;
}
