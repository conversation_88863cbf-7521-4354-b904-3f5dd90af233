<div class="container mt-4">
    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3>Kullanıcıya Rol Ata</h3>
          </div>
          <div class="card-body">
            <form [formGroup]="claimForm" (ngSubmit)="addUserOperationClaim()">
              <div class="form-group">
                <label for="userId">Kullanıcı Adı</label>
                <select class="form-control" id="userId" formControlName="userId">
                  <option value="">Kullanıcı Seçin</option>
                  <option *ngFor="let user of users" [value]="user.userID">
                    {{ user.firstName }}-----{{user.email}}
                  </option>
                </select>
              </div>
              <div class="form-group mt-3">
                <label for="operationClaimId">Rol</label>
                <select class="form-control" id="operationClaimId" formControlName="operationClaimId">
                  <option value="">Rol <PERSON></option>
                  <option *ngFor="let claim of operationClaims" [value]="claim.operationClaimId">
                    {{ claim.name }}
                  </option>
                </select>
              </div>
              <button type="submit" class="btn btn-primary mt-3" [disabled]="!claimForm.valid || isLoading">
                {{ isLoading ? 'Ekleniyor...' : 'Rol Ata' }}
              </button>
            </form>
          </div>
        </div>
      </div>
  
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3>Kullanıcı Rolleri</h3>
          </div>
          <div class="card-body">
            <table class="table">
              <thead>
                <tr>
                  <th>Kullanıcı Adı</th>
                  <th>E Posta</th>
                  <th>Rol</th>
                  <th>İşlem</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let claim of userOperationClaims">
                  <td>{{ claim.userName }}</td>
                  <td>{{ claim.email }}</td>
                  <td>{{ claim.operationClaimName }}</td>
                  <td>
                    <button class="btn btn-danger btn-sm" (click)="deleteUserOperationClaim(claim)">Sil</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>