
import { CanActivateFn, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth.service';
import { inject, Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class LoginGuardService {
  private isBrowser: boolean;

  constructor(
    private authService: AuthService,
    private toastrService: ToastrService,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    // If not in browser, allow server-side rendering to proceed
    if (!this.isBrowser) {
      return true;
    }

    if (this.authService.isAuthenticated()) {
      return true;
    } else {
      this.router.navigate(["login"]);
      
      // Only check localStorage and show toastr in browser environment
      if (this.isBrowser && !localStorage.getItem('token')) {
        this.toastrService.info("Sisteme giriş yapmalısınız");
      }
      return false;
    }
  }
}

export const LoginGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
) => {
  return inject(LoginGuardService).canActivate(route, state);
};
