
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: '7536914cef786fddf0b3a869208d840c5ed216772a6f7bf64efa85bb310a1b4c', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '1e40321243ba8fc737fa8a48d1cf676be4e31f748c62657fe792a1a3fe30a76d', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-TBXNKKXU.css': {size: 298105, hash: '7i4JWBKmeRM', text: () => import('./assets-chunks/styles-TBXNKKXU_css.mjs').then(m => m.default)}
  },
};
