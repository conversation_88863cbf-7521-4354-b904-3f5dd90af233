import { Injectable } from '@angular/core';
import { CanActivate, Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PasswordChangeGuard implements CanActivate {
  
  constructor(private router: Router) {}
  
  canActivate(): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    const requirePasswordChange = localStorage.getItem('requirePasswordChange');
    
    if (requirePasswordChange === 'true') {
      // Şifre değiştirme zorunluluğu varsa, şifre değiştirme sayfasına yönlendir
      return this.router.createUrlTree(['/change-password']);
    }
    
    // Şifre değiştirme zorunluluğu yoksa, sayfaya erişime izin ver
    return true;
  }
}