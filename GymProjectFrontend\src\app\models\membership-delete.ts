// Üyenin tüm aktif üyeliklerini listeleyen model
export interface MemberActiveMemberships {
  memberID: number;
  memberName: string;
  phoneNumber: string;
  gender: number;
  activeMemberships: MembershipDeleteOption[];
  totalActiveMemberships: number;
  totalPaidAmount: number;
  hasMultipleMemberships: boolean;
}

// Silinebilir üyelik seçeneği
export interface MembershipDeleteOption {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  day: number;
  startDate: string;
  endDate: string;
  remainingDays: number;
  isFrozen: boolean;
  freezeEndDate?: string;
  totalPaidAmount: number;
  paymentCount: number;
  lastPaymentDate: string;
  paymentMethods: string;
  canBeDeleted: boolean;
  deleteWarning: string;
}

// Üyelik silme isteği
export interface MembershipDeleteRequest {
  memberID: number;
  membershipIDsToDelete: number[];
  deleteAllMemberships: boolean;
  deleteReason: string;
  confirmPaymentLoss: boolean;
}

// Üyelik silme sonucu
export interface MembershipDeleteResult {
  success: boolean;
  message: string;
  deletedMembershipsCount: number;
  refundableAmount: number;
  deletedMembershipDetails: string[];
  warnings: string[];
  requiresManagerApproval: boolean;
}
