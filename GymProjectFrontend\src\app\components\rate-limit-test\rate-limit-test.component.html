<div class="rate-limit-test-container">
  <!-- Header -->
  <div class="test-header">
    <h2 class="page-title">
      <i class="fas fa-shield-alt"></i>
      Rate Limit Test
    </h2>
    <p class="page-description">
      API endpoint'lerinin rate limit kurallarını test edin. 1 dakikada 100'den fazla istek göndererek rate limit sistemini kontrol edebilirsiniz.
    </p>
  </div>

  <!-- Test Konfigürasyonu -->
  <div class="test-config-card">
    <h3 class="card-title">
      <i class="fas fa-cog"></i>
      Test Konfigürasyonu
    </h3>

    <div class="config-grid">
      <div class="config-item">
        <label for="endpoint-select">Test Endpoint'i:</label>
        <select
          id="endpoint-select"
          [(ngModel)]="selectedEndpoint"
          (change)="onEndpointChange()"
          [disabled]="isTestRunning"
          class="form-select">
          <option *ngFor="let endpoint of testEndpoints" [value]="endpoint.value">
            {{endpoint.label}} ({{endpoint.limit}})
          </option>
        </select>
      </div>

      <div class="config-item">
        <label for="total-requests">Toplam İstek Sayısı:</label>
        <input
          id="total-requests"
          type="number"
          [(ngModel)]="totalRequests"
          [disabled]="isTestRunning"
          min="1"
          max="500"
          class="form-input">
      </div>

      <div class="config-item">
        <label for="request-interval">İstek Aralığı (ms):</label>
        <input
          id="request-interval"
          type="number"
          [(ngModel)]="requestInterval"
          [disabled]="isTestRunning"
          min="10"
          max="5000"
          class="form-input">
      </div>
    </div>
  </div>

  <!-- Test Kontrolleri -->
  <div class="test-controls">
    <button
      class="btn btn-primary"
      [disabled]="isTestRunning"
      (click)="startTest()">
      <i class="fas fa-play"></i>
      Test Başlat
    </button>

    <button
      class="btn btn-danger"
      [disabled]="!isTestRunning"
      (click)="stopTest()">
      <i class="fas fa-stop"></i>
      Test Durdur
    </button>

    <button
      class="btn btn-secondary"
      [disabled]="isTestRunning || testResults.length === 0"
      (click)="clearResults()">
      <i class="fas fa-trash"></i>
      Sonuçları Temizle
    </button>

    <button
      class="btn btn-success"
      [disabled]="testResults.length === 0"
      (click)="exportResults()">
      <i class="fas fa-download"></i>
      CSV İndir
    </button>
  </div>

  <!-- Test Durumu -->
  <div class="test-status" *ngIf="isTestRunning || currentRequestCount > 0">
    <div class="status-card">
      <h3 class="status-title">
        <i class="fas fa-chart-line"></i>
        Test Durumu
      </h3>

      <div class="status-grid">
        <div class="status-item">
          <span class="status-label">İlerleme:</span>
          <div class="progress-container">
            <div class="progress-bar">
              <div
                class="progress-fill"
                [style.width.%]="(currentRequestCount / totalRequests) * 100">
              </div>
            </div>
            <span class="progress-text">{{currentRequestCount}} / {{totalRequests}}</span>
          </div>
        </div>

        <div class="status-item">
          <span class="status-label">Başarılı:</span>
          <span class="status-value success">{{successCount}}</span>
        </div>

        <div class="status-item">
          <span class="status-label">Rate Limited:</span>
          <span class="status-value rate-limited">{{rateLimitCount}}</span>
        </div>

        <div class="status-item">
          <span class="status-label">Hata:</span>
          <span class="status-value error">{{errorCount}}</span>
        </div>

        <div class="status-item" *ngIf="averageResponseTime > 0">
          <span class="status-label">Ort. Yanıt Süresi:</span>
          <span class="status-value">{{averageResponseTime}}ms</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Sonuçları -->
  <div class="test-results" *ngIf="testResults.length > 0">
    <div class="results-card">
      <h3 class="results-title">
        <i class="fas fa-list"></i>
        Test Sonuçları (Son 50 İstek)
      </h3>

      <div class="results-table-container">
        <table class="results-table">
          <thead>
            <tr>
              <th>İstek No</th>
              <th>Zaman</th>
              <th>Durum</th>
              <th>Status Code</th>
              <th>Mesaj</th>
              <th>Yanıt Süresi</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let result of testResults" [class]="getStatusClass(result.status)">
              <td>{{result.requestNumber}}</td>
              <td>{{result.timestamp}}</td>
              <td>
                <span class="status-badge" [class]="getStatusClass(result.status)">
                  {{getStatusIcon(result.status)}} {{result.status}}
                </span>
              </td>
              <td>{{result.statusCode || '-'}}</td>
              <td class="message-cell">{{result.message || '-'}}</td>
              <td>{{result.responseTime ? result.responseTime + 'ms' : '-'}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Rate Limit Bilgileri -->
  <div class="rate-limit-info">
    <div class="info-card">
      <h3 class="info-title">
        <i class="fas fa-info-circle"></i>
        Rate Limit Kuralları
      </h3>

      <div class="info-content">
        <h4>Genel API Limitleri (AspNetCoreRateLimit):</h4>
        <ul>
          <li><strong>Genel:</strong> 1 dakikada 100 istek, 1 saatte 1000 istek</li>
          <li><strong>User GetAll:</strong> 1 dakikada 30 istek</li>
          <li><strong>Member GetAll:</strong> 1 dakikada 30 istek</li>
          <li><strong>User Profile:</strong> 1 dakikada 15 istek</li>
          <li><strong>Member Scan:</strong> 10 saniyede 1 istek</li>
          <li><strong>Change Password:</strong> 5 dakikada 5 istek</li>
          <li><strong>Today Entries:</strong> 30 saniyede 2 istek</li>
          <li><strong>Payment History:</strong> 30 saniyede 5 istek</li>
          <li><strong>Products GetAll:</strong> 1 dakikada 20 istek</li>
          <li><strong>Transactions GetAll:</strong> 30 saniyede 5 istek</li>
          <li><strong>Add Transaction:</strong> 5 saniyede 1 istek</li>
          <li><strong>Add Product:</strong> 30 saniyede 3 istek</li>
          <li><strong>Add Member:</strong> 30 saniyede 2 istek</li>
        </ul>

        <h4>Özel Rate Limiting:</h4>
        <ul>
          <li><strong>Login:</strong> 5 başarısız deneme → Progressive ban (5dk, 15dk, 1sa, 6sa, 24sa)</li>
          <li><strong>Register:</strong> Günde 3 kayıt → Progressive ban (1sa, 6sa, 24sa, 7gün)</li>
          <li><strong>Profil Fotoğrafı:</strong> Günde 3 yükleme</li>
        </ul>

        <div class="warning-box">
          <i class="fas fa-exclamation-triangle"></i>
          <strong>Uyarı:</strong> Bu test gerçek API'ye istek gönderir. Rate limit aşımı durumunda geçici olarak engellenebilirsiniz.
        </div>
      </div>
    </div>
  </div>
</div>
