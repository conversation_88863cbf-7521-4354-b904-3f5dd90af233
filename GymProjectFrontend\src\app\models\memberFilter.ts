export interface MemberFilter {
    memberID: number;
    membershipID: number;
    membershipTypeID?: number;
    name: string;
    gender: number;
    phoneNumber: string;
    typeName?: string;
    branch: string;
    startDate: string;
    endDate: string;
    remainingDays: number;
    isFutureStartDate: boolean; // Üyeliğin başlangıç tarihi gelecekte mi
    isActive: boolean;
    updatedDate?: string;
}
  
  