import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { MembershipDeleteService } from '../../services/membership-delete.service';
import { 
  MemberActiveMemberships, 
  MembershipDeleteOption, 
  MembershipDeleteRequest,
  MembershipDeleteResult 
} from '../../models/membership-delete';
import { faExclamationTriangle, faTrashAlt, faInfoCircle, faCheckCircle } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-smart-delete-dialog',
  templateUrl: './smart-delete-dialog.component.html',
  styleUrls: ['./smart-delete-dialog.component.scss'],
  standalone: false
})
export class SmartDeleteDialogComponent implements OnInit {
  
  deleteForm: FormGroup;
  memberData: MemberActiveMemberships | null = null;
  selectedMembershipIds: number[] = [];
  isLoading = false;
  isDeleting = false;
  deleteResult: MembershipDeleteResult | null = null;
  showResult = false;

  // Icons
  faExclamationTriangle = faExclamationTriangle;
  faTrashAlt = faTrashAlt;
  faInfoCircle = faInfoCircle;
  faCheckCircle = faCheckCircle;

  constructor(
    private dialogRef: MatDialogRef<SmartDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { memberId: number },
    private fb: FormBuilder,
    private membershipDeleteService: MembershipDeleteService,
    private toastrService: ToastrService
  ) {
    this.deleteForm = this.fb.group({
      deleteReason: ['', Validators.required],
      confirmPaymentLoss: [false],
      deleteAllMemberships: [false]
    });
  }

  ngOnInit(): void {
    this.loadMemberActiveMemberships();
  }

  /**
   * Üyenin aktif üyeliklerini yükler
   */
  loadMemberActiveMemberships(): void {
    this.isLoading = true;
    
    this.membershipDeleteService.getMemberActiveMembershipsForDelete(this.data.memberId)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.memberData = response.data;
            
            // Eğer tek üyelik varsa otomatik seç
            if (this.memberData.activeMemberships.length === 1) {
              this.selectedMembershipIds = [this.memberData.activeMemberships[0].membershipID];
            }
          } else {
            this.toastrService.error('Üyelik bilgileri yüklenemedi.', 'Hata');
            this.dialogRef.close();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading member memberships:', error);
          this.toastrService.error('Üyelik bilgileri yüklenirken hata oluştu.', 'Hata');
          this.isLoading = false;
          this.dialogRef.close();
        }
      });
  }

  /**
   * Üyelik seçimini toggle eder
   */
  toggleMembershipSelection(membershipId: number): void {
    const index = this.selectedMembershipIds.indexOf(membershipId);
    if (index > -1) {
      this.selectedMembershipIds.splice(index, 1);
    } else {
      this.selectedMembershipIds.push(membershipId);
    }
    
    // Form kontrolünü güncelle
    this.deleteForm.patchValue({
      deleteAllMemberships: this.selectedMembershipIds.length === this.memberData?.activeMemberships.length
    });
  }

  /**
   * Tüm üyelikleri seç/seçimi kaldır
   */
  toggleAllMemberships(): void {
    const deleteAll = this.deleteForm.get('deleteAllMemberships')?.value;
    
    if (deleteAll) {
      this.selectedMembershipIds = this.memberData?.activeMemberships.map(m => m.membershipID) || [];
    } else {
      this.selectedMembershipIds = [];
    }
  }

  /**
   * Seçilen üyelik var mı kontrol eder
   */
  isMembershipSelected(membershipId: number): boolean {
    return this.selectedMembershipIds.includes(membershipId);
  }

  /**
   * Toplam seçilen ödeme tutarını hesaplar
   */
  getTotalSelectedPaymentAmount(): number {
    if (!this.memberData) return 0;
    
    return this.memberData.activeMemberships
      .filter(m => this.selectedMembershipIds.includes(m.membershipID))
      .reduce((total, m) => total + m.totalPaidAmount, 0);
  }

  /**
   * Silme işlemini gerçekleştirir
   */
  performDelete(): void {
    if (!this.memberData || this.selectedMembershipIds.length === 0) {
      this.toastrService.warning('Lütfen silinecek üyelikleri seçin.', 'Uyarı');
      return;
    }

    if (!this.deleteForm.valid) {
      this.toastrService.warning('Lütfen tüm gerekli alanları doldurun.', 'Uyarı');
      return;
    }

    const totalPaymentLoss = this.getTotalSelectedPaymentAmount();
    if (totalPaymentLoss > 0 && !this.deleteForm.get('confirmPaymentLoss')?.value) {
      this.toastrService.warning('Ödeme kaybını onaylamanız gerekiyor.', 'Uyarı');
      return;
    }

    this.isDeleting = true;

    const deleteRequest: MembershipDeleteRequest = {
      memberID: this.data.memberId,
      membershipIDsToDelete: this.selectedMembershipIds,
      deleteAllMemberships: this.deleteForm.get('deleteAllMemberships')?.value || false,
      deleteReason: this.deleteForm.get('deleteReason')?.value,
      confirmPaymentLoss: this.deleteForm.get('confirmPaymentLoss')?.value || false
    };

    this.membershipDeleteService.deleteMultipleMemberships(deleteRequest)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.deleteResult = response.data;
            this.showResult = true;
            
            if (this.deleteResult.success) {
              this.toastrService.success(this.deleteResult.message, 'Başarılı');
            } else {
              this.toastrService.error(this.deleteResult.message, 'Hata');
            }
          } else {
            this.toastrService.error('Silme işlemi başarısız.', 'Hata');
          }
          this.isDeleting = false;
        },
        error: (error) => {
          console.error('Error deleting memberships:', error);
          this.toastrService.error('Silme işlemi sırasında hata oluştu.', 'Hata');
          this.isDeleting = false;
        }
      });
  }

  /**
   * Dialog'u kapatır
   */
  closeDialog(): void {
    this.dialogRef.close(this.deleteResult?.success || false);
  }

  /**
   * Kalan gün rengini belirler
   */
  getRemainingDaysClass(days: number): string {
    if (days <= 3) return 'text-danger';
    if (days <= 10) return 'text-warning';
    return 'text-success';
  }

  /**
   * Ödeme tutarını formatlar
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  }
}
