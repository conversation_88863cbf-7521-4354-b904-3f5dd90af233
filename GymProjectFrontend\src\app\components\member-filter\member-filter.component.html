<div class="container mt-4">
  <!-- Loading Spinner -->
  <div
    class="d-flex justify-content-center align-items-center"
    *ngIf="isLoading"
    style="height: 100vh"
  >
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <!-- Filtreler ve İstatistikler -->
    <div class="col-md-3">
      <div class="modern-card filter-card slide-in-left">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filtreler
          </h5>
        </div>
        
        <div class="modern-card-body">
          <!-- Aktif Filtre Özeti (En Üstte) -->
          <div class="active-filter-summary mb-3" *ngIf="branchFilter || selectedBranch || selectedMembershipTypeID">
            <div class="filter-summary-header">
              <i class="fas fa-filter me-2"></i>
              <span>Aktif Filtre:</span>
            </div>
            <div class="filter-summary-content">
              <span *ngIf="branchFilter && !selectedBranch && !selectedMembershipTypeID">
                {{ branchFilter }} - Tüm Paketler
              </span>
              <span *ngIf="selectedBranch && !selectedMembershipTypeID">
                {{ selectedBranch }} - Tüm Paketler
              </span>
              <span *ngIf="selectedMembershipTypeID">
                {{ selectedBranch }} - {{ getSelectedPackageName() }}
              </span>
              <button
                class="modern-btn modern-btn-outline-danger modern-btn-xs ms-2"
                [disabled]="isProcessing"
                (click)="resetAllFilters()">
                <i class="fas fa-times" *ngIf="!isProcessing"></i>
                <i class="fas fa-spinner fa-spin" *ngIf="isProcessing"></i>
              </button>
            </div>
          </div>

          <!-- Cinsiyet Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-venus-mars me-2"></i>
              Cinsiyet Filtreleri
            </h6>
            
            <div class="modern-radio-group">
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-all"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  [value]="''"
                  (change)="onFilterChange()"
                />
                <label class="modern-radio-label" for="gender-all">
                  <span class="radio-icon"></span>
                  Tümü
                </label>
              </div>
              
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-male"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  value="1"
                  (change)="onFilterChange()"
                />
                <label class="modern-radio-label" for="gender-male">
                  <span class="radio-icon"></span>
                  <i class="fas fa-male text-primary me-1"></i>
                  Erkek
                  <span class="modern-badge modern-badge-primary ms-2">{{ genderCounts.male }}</span>
                </label>
              </div>
              
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-female"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  value="2"
                  (change)="onFilterChange()"
                />
                <label class="modern-radio-label" for="gender-female">
                  <span class="radio-icon"></span>
                  <i class="fas fa-female text-danger me-1"></i>
                  Kadın
                  <span class="modern-badge modern-badge-danger ms-2">{{ genderCounts.female }}</span>
                </label>
              </div>
            </div>
          </div>
          
          <!-- Branş ve Paket Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-dumbbell me-2"></i>
              Branş ve Paket Filtreleri
            </h6>

            <!-- Tümü Seçeneği -->
            <div class="branch-accordion-item"
                 [class.active]="!branchFilter && !selectedBranch"
                 [class.processing]="isProcessing">
              <div class="branch-header" (click)="selectAllBranches()">
                <div class="branch-info">
                  <i class="fas fa-globe me-2"></i>
                  <span class="branch-name">Tüm Branşlar</span>
                  <span class="modern-badge modern-badge-secondary ms-2">{{ totalActiveMembers }}</span>
                </div>
                <div class="branch-actions">
                  <i class="fas fa-check text-success" *ngIf="!branchFilter && !selectedBranch && !isProcessing"></i>
                  <i class="fas fa-spinner fa-spin text-primary" *ngIf="isProcessing"></i>
                </div>
              </div>
            </div>

            <!-- Branş Accordion'ları -->
            <div
              *ngFor="let branchData of branchPackageFilters"
              class="branch-accordion-item"
              [class.active]="branchFilter === branchData.branch || selectedBranch === branchData.branch"
              [class.expanded]="expandedBranch === branchData.branch"
              [class.processing]="isProcessing">

              <!-- Branş Header -->
              <div class="branch-header">
                <!-- Branş Seçim Alanı -->
                <div class="branch-select-area" (click)="selectBranchOnly(branchData.branch)">
                  <div class="branch-info">
                    <i class="fas fa-dumbbell me-2"></i>
                    <span class="branch-name">{{ branchData.branch }}</span>
                    <span class="modern-badge modern-badge-primary ms-2">{{ branchData.totalMembers }}</span>
                  </div>
                  <div class="branch-status">
                    <i class="fas fa-check text-success" *ngIf="(branchFilter === branchData.branch || selectedBranch === branchData.branch) && !selectedMembershipTypeID && !isProcessing"></i>
                    <i class="fas fa-spinner fa-spin text-primary" *ngIf="isProcessing"></i>
                  </div>
                </div>

                <!-- Dropdown Toggle Alanı -->
                <div class="dropdown-toggle-area" (click)="toggleBranchAccordion(branchData.branch)">
                  <i class="fas fa-chevron-down transition-icon"
                     [class.rotated]="expandedBranch === branchData.branch"
                     *ngIf="!isProcessing"></i>
                  <i class="fas fa-spinner fa-spin text-secondary" *ngIf="isProcessing"></i>
                </div>
              </div>

              <!-- Paket Listesi (Accordion Content) -->
              <div class="package-list" *ngIf="expandedBranch === branchData.branch">
                <div class="package-header">
                  <button
                    class="package-item package-all"
                    [class.active]="(branchFilter === branchData.branch || selectedBranch === branchData.branch) && !selectedMembershipTypeID"
                    [class.processing]="isProcessing"
                    [disabled]="isProcessing"
                    (click)="selectBranchOnly(branchData.branch)">
                    <i class="fas fa-layer-group me-2" *ngIf="!isProcessing"></i>
                    <i class="fas fa-spinner fa-spin me-2" *ngIf="isProcessing"></i>
                    <span>Tüm {{ branchData.branch }} Paketleri</span>
                    <span class="modern-badge modern-badge-info ms-auto">{{ branchData.totalMembers }}</span>
                  </button>
                </div>

                <div class="package-items">
                  <button
                    *ngFor="let package of branchData.packages"
                    class="package-item"
                    [class.active]="selectedMembershipTypeID === package.membershipTypeID"
                    [class.processing]="isProcessing"
                    [disabled]="isProcessing"
                    (click)="selectSpecificPackage(branchData.branch, package.membershipTypeID)">
                    <i class="fas fa-tag me-2" *ngIf="!isProcessing"></i>
                    <i class="fas fa-spinner fa-spin me-2" *ngIf="isProcessing"></i>
                    <div class="package-details">
                      <span class="package-name">{{ package.typeName }}</span>
                      <small class="package-duration">{{ package.day }} gün</small>
                    </div>
                    <span class="modern-badge modern-badge-success ms-auto">{{ package.memberCount }}</span>
                  </button>
                </div>
              </div>
            </div>


          </div>


          
          <!-- Üye İstatistikleri -->
          <div class="modern-stats-card mt-4">
            <div class="modern-stats-icon bg-primary">
              <i class="fas fa-users"></i>
            </div>
            <div class="modern-stats-info">
              <h2 class="modern-stats-value">{{ totalActiveMembers }}</h2>
              <p class="modern-stats-label">Toplam Aktif Üye</p>
            </div>
          </div>
          
        </div>
      </div>
      
      <!-- Cinsiyet Dağılımı Grafiği -->
      <div class="modern-card gender-chart-card mt-3 slide-in-left">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-chart-pie me-2"></i>
            Cinsiyet Dağılımı
          </h5>
        </div>
        <div class="modern-card-body">
          <canvas id="genderChart" width="100%" height="180"></canvas>
        </div>
      </div>
    </div>

    <!-- Üye Listesi -->
    <div class="col-md-9">
      <div class="modern-card member-list-card fade-in">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Aktif Üye Listesi
          </h5>
          
          <!-- Arama Kutusu -->
          <div class="search-container">
            <div class="modern-search-input">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                [ngModel]="memberFilterText"
                (ngModelChange)="searchTextChanged($event)"
                placeholder="Ad, Soyad veya Telefon"
              />
            </div>
          </div>
        </div>
        
        <div class="modern-card-body">
          <div class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-user me-2"></i>
                    Ad Soyad
                  </th>
                  <th>
                    <i class="fas fa-phone me-2"></i>
                    Telefon
                  </th>
                  <th>
                    <i class="fas fa-dumbbell me-2"></i>
                    Branş / Paket
                  </th>
                  <th>
                    <i class="fas fa-calendar-day me-2"></i>
                    Kalan Gün
                  </th>
                  <th>
                    <i class="fas fa-cogs me-2"></i>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let member of members" class="zoom-in">
                  <td>
                    <div class="member-name">
                      <div class="modern-avatar" [ngStyle]="{'background-color': member.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
                        {{ member.name.charAt(0) }}
                      </div>
                      <span>{{ member.name }}</span>
                    </div>
                  </td>
                  <td>{{ member.phoneNumber }}</td>
                  <td>
                    <div class="branch-package-info">
                      <span class="modern-badge modern-badge-info">{{ member.branch }}</span>
                      <small class="text-muted d-block" *ngIf="member.typeName">{{ member.typeName }}</small>
                      <!-- Çoklu üyelik göstergesi -->
                      <span class="modern-badge modern-badge-warning modern-badge-sm mt-1"
                            *ngIf="hasMultipleMemberships(member.memberID)"
                            title="Bu üyenin birden fazla aktif üyeliği var">
                        <i class="fas fa-layer-group me-1"></i>
                        Çoklu
                      </span>
                    </div>
                  </td>
                  <td>
                    <div class="remaining-days">
                      <span [ngClass]="{'text-success': member.remainingDays > 10, 'text-warning': member.remainingDays <= 10 && member.remainingDays > 3, 'text-danger': member.remainingDays <= 3}">
                        {{ member.remainingDays }} gün
                      </span>
                      <span *ngIf="member.isFutureStartDate" class="modern-badge modern-badge-warning ms-2">
                        Başlangıç: {{ member.startDate | date:'dd.MM.yyyy' }}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button
                        class="modern-btn modern-btn-danger modern-btn-sm"
                        (click)="deleteMember(member)"
                        title="Üyeyi Sil"
                      >
                        <i class="fas fa-trash-alt"></i>
                      </button>
                      <button
                        class="modern-btn modern-btn-info modern-btn-sm ms-2"
                        (click)="openFreezeDialog(member)"
                        [disabled]="member.remainingDays <= 0"
                        title="Üyeliği Dondur"
                      >
                        <i class="fas fa-snowflake"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                
                <!-- Veri yoksa gösterilecek mesaj -->
                <tr *ngIf="members.length === 0">
                  <td colspan="5" class="text-center py-4">
                    <i class="fas fa-search fa-2x mb-2 text-muted"></i>
                    <p class="mb-0">Arama kriterlerine uygun üye bulunamadı.</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <!-- Sayfalama -->
          <div class="pagination-container" *ngIf="totalPages > 1">
            <ul class="modern-pagination">
              <li class="modern-page-item" [class.disabled]="currentPage === 1">
                <a
                  class="modern-page-link"
                  href="javascript:void(0)"
                  (click)="onPageChange(currentPage - 1)"
                >
                  <i class="fas fa-chevron-left"></i>
                </a>
              </li>
              <li
                class="modern-page-item"
                *ngFor="let i of [].constructor(totalPages); let idx = index"
                [class.active]="currentPage === idx + 1"
              >
                <a
                  class="modern-page-link"
                  href="javascript:void(0)"
                  (click)="onPageChange(idx + 1)"
                >
                  {{ idx + 1 }}
                </a>
              </li>
              <li
                class="modern-page-item"
                [class.disabled]="currentPage === totalPages"
              >
                <a
                  class="modern-page-link"
                  href="javascript:void(0)"
                  (click)="onPageChange(currentPage + 1)"
                >
                  <i class="fas fa-chevron-right"></i>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
