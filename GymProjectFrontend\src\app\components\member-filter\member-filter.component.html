<div class="container mt-4">
  <!-- Loading Spinner -->
  <div
    class="d-flex justify-content-center align-items-center"
    *ngIf="isLoading"
    style="height: 100vh"
  >
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <!-- Filtreler ve İstatistikler -->
    <div class="col-md-3">
      <div class="modern-card filter-card slide-in-left">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filtreler
          </h5>
        </div>
        
        <div class="modern-card-body">
          <!-- Aktif Filtre Özeti -->
          <div class="active-filter-summary mb-3" *ngIf="activeFilter.isActive">
            <div class="filter-summary-header">
              <i class="fas fa-filter me-2"></i>
              <span>Aktif Filtre:</span>
            </div>
            <div class="filter-summary-content">
              <span>{{ activeFilter.displayText }}</span>
              <button
                class="modern-btn modern-btn-outline-danger modern-btn-xs ms-2"
                [disabled]="isProcessing"
                (click)="clearAllFilters()">
                <i class="fas fa-times" *ngIf="!isProcessing"></i>
                <i class="fas fa-spinner fa-spin" *ngIf="isProcessing"></i>
              </button>
            </div>
          </div>

          <!-- Cinsiyet Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-venus-mars me-2"></i>
              Cinsiyet Filtreleri
            </h6>
            
            <div class="modern-radio-group">
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-all"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  [value]="''"
                  (change)="onGenderChange()"
                />
                <label class="modern-radio-label" for="gender-all">
                  <span class="radio-icon"></span>
                  Tümü
                </label>
              </div>
              
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-male"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  value="1"
                  (change)="onGenderChange()"
                />
                <label class="modern-radio-label" for="gender-male">
                  <span class="radio-icon"></span>
                  <i class="fas fa-male text-primary me-1"></i>
                  Erkek
                  <span class="modern-badge modern-badge-primary ms-2">{{ genderCounts.male }}</span>
                </label>
              </div>
              
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-female"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  value="2"
                  (change)="onGenderChange()"
                />
                <label class="modern-radio-label" for="gender-female">
                  <span class="radio-icon"></span>
                  <i class="fas fa-female text-danger me-1"></i>
                  Kadın
                  <span class="modern-badge modern-badge-danger ms-2">{{ genderCounts.female }}</span>
                </label>
              </div>
            </div>
          </div>
          
          <!-- Branş ve Paket Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-dumbbell me-2"></i>
              Branş ve Paket Filtreleri
            </h6>

            <!-- Tümü Seçeneği -->
            <div class="filter-item"
                 [class.active]="!currentFilter.branchName"
                 [class.processing]="isProcessing">
              <button class="filter-button"
                      [disabled]="isProcessing"
                      (click)="selectAllBranches()">
                <div class="filter-content">
                  <i class="fas fa-globe me-2"></i>
                  <span>Tüm Branşlar</span>
                  <span class="modern-badge modern-badge-secondary ms-auto">{{ totalActiveMembers }}</span>
                </div>
                <div class="filter-status">
                  <i class="fas fa-check text-success" *ngIf="!currentFilter.branchName && !isProcessing"></i>
                  <i class="fas fa-spinner fa-spin text-primary" *ngIf="isProcessing"></i>
                </div>
              </button>
            </div>

            <!-- Branş Listesi -->
            <div *ngFor="let branch of branches" class="filter-group">
              <!-- Branş Seçimi -->
              <div class="filter-item"
                   [class.active]="currentFilter.branchName === branch.name && !currentFilter.packageId"
                   [class.processing]="isProcessing">
                <button class="filter-button"
                        [disabled]="isProcessing"
                        (click)="selectBranch(branch.name)">
                  <div class="filter-content">
                    <i class="fas fa-dumbbell me-2"></i>
                    <span>{{ branch.name }}</span>
                    <span class="modern-badge modern-badge-primary ms-auto">{{ branch.totalMembers }}</span>
                  </div>
                  <div class="filter-actions">
                    <i class="fas fa-check text-success me-2"
                       *ngIf="currentFilter.branchName === branch.name && !currentFilter.packageId && !isProcessing"></i>
                    <i class="fas fa-spinner fa-spin text-primary me-2" *ngIf="isProcessing"></i>
                    <button class="dropdown-toggle"
                            [class.expanded]="expandedBranch === branch.name"
                            (click)="toggleBranchDropdown(branch.name, $event)">
                      <i class="fas fa-chevron-down"></i>
                    </button>
                  </div>
                </button>
              </div>

              <!-- Paket Listesi -->
              <div class="package-list" *ngIf="expandedBranch === branch.name">
                <div class="package-item"
                     *ngFor="let package of branch.packages"
                     [class.active]="currentFilter.packageId === package.id"
                     [class.processing]="isProcessing">
                  <button class="package-button"
                          [disabled]="isProcessing"
                          (click)="selectPackage(branch.name, package.id, package.name)">
                    <div class="package-content">
                      <i class="fas fa-tag me-2"></i>
                      <div class="package-info">
                        <span class="package-name">{{ package.name }}</span>
                        <small class="package-duration">{{ package.duration }} gün</small>
                      </div>
                      <span class="modern-badge modern-badge-success ms-auto">{{ package.memberCount }}</span>
                    </div>
                    <div class="package-status">
                      <i class="fas fa-check text-success"
                         *ngIf="currentFilter.packageId === package.id && !isProcessing"></i>
                      <i class="fas fa-spinner fa-spin text-primary" *ngIf="isProcessing"></i>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>


          
          <!-- Üye İstatistikleri -->
          <div class="modern-stats-card mt-4">
            <div class="modern-stats-icon bg-primary">
              <i class="fas fa-users"></i>
            </div>
            <div class="modern-stats-info">
              <h2 class="modern-stats-value">{{ totalActiveMembers }}</h2>
              <p class="modern-stats-label">Toplam Aktif Üye</p>
            </div>
          </div>
          
        </div>
      </div>
      
      <!-- Cinsiyet Dağılımı Grafiği -->
      <div class="modern-card gender-chart-card mt-3 slide-in-left">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-chart-pie me-2"></i>
            Cinsiyet Dağılımı
          </h5>
        </div>
        <div class="modern-card-body">
          <canvas id="genderChart" width="100%" height="180"></canvas>
        </div>
      </div>
    </div>

    <!-- Üye Listesi -->
    <div class="col-md-9">
      <div class="modern-card member-list-card fade-in">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Aktif Üye Listesi
          </h5>
          
          <!-- Arama Kutusu -->
          <div class="search-container">
            <div class="modern-search-input">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                [ngModel]="memberFilterText"
                (ngModelChange)="searchTextChanged($event)"
                placeholder="Ad, Soyad veya Telefon"
              />
            </div>
          </div>
        </div>
        
        <div class="modern-card-body">
          <div class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-user me-2"></i>
                    Ad Soyad
                  </th>
                  <th>
                    <i class="fas fa-phone me-2"></i>
                    Telefon
                  </th>
                  <th>
                    <i class="fas fa-dumbbell me-2"></i>
                    Branş / Paket
                  </th>
                  <th>
                    <i class="fas fa-calendar-day me-2"></i>
                    Kalan Gün
                  </th>
                  <th>
                    <i class="fas fa-cogs me-2"></i>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let member of members" class="zoom-in">
                  <td>
                    <div class="member-name">
                      <div class="modern-avatar" [ngStyle]="{'background-color': member.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
                        {{ member.name.charAt(0) }}
                      </div>
                      <span>{{ member.name }}</span>
                    </div>
                  </td>
                  <td>{{ member.phoneNumber }}</td>
                  <td>
                    <div class="branch-package-info">
                      <span class="modern-badge modern-badge-info">{{ member.branch }}</span>
                      <small class="text-muted d-block" *ngIf="member.typeName">{{ member.typeName }}</small>
                      <!-- Çoklu üyelik göstergesi -->
                      <span class="modern-badge modern-badge-warning modern-badge-sm mt-1"
                            *ngIf="hasMultipleMemberships(member.memberID)"
                            title="Bu üyenin birden fazla aktif üyeliği var">
                        <i class="fas fa-layer-group me-1"></i>
                        Çoklu
                      </span>
                    </div>
                  </td>
                  <td>
                    <div class="remaining-days">
                      <span [ngClass]="{'text-success': member.remainingDays > 10, 'text-warning': member.remainingDays <= 10 && member.remainingDays > 3, 'text-danger': member.remainingDays <= 3}">
                        {{ member.remainingDays }} gün
                      </span>
                      <span *ngIf="member.isFutureStartDate" class="modern-badge modern-badge-warning ms-2">
                        Başlangıç: {{ member.startDate | date:'dd.MM.yyyy' }}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button
                        class="modern-btn modern-btn-danger modern-btn-sm"
                        (click)="deleteMember(member)"
                        title="Üyeyi Sil"
                      >
                        <i class="fas fa-trash-alt"></i>
                      </button>
                      <button
                        class="modern-btn modern-btn-info modern-btn-sm ms-2"
                        (click)="openFreezeDialog(member)"
                        [disabled]="member.remainingDays <= 0"
                        title="Üyeliği Dondur"
                      >
                        <i class="fas fa-snowflake"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                
                <!-- Veri yoksa gösterilecek mesaj -->
                <tr *ngIf="members.length === 0">
                  <td colspan="5" class="text-center py-4">
                    <i class="fas fa-search fa-2x mb-2 text-muted"></i>
                    <p class="mb-0">Arama kriterlerine uygun üye bulunamadı.</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <!-- Sayfalama -->
          <div class="pagination-container" *ngIf="totalPages > 1">
            <ul class="modern-pagination">
              <li class="modern-page-item" [class.disabled]="currentPage === 1">
                <a
                  class="modern-page-link"
                  href="javascript:void(0)"
                  (click)="onPageChange(currentPage - 1)"
                >
                  <i class="fas fa-chevron-left"></i>
                </a>
              </li>
              <li
                class="modern-page-item"
                *ngFor="let i of [].constructor(totalPages); let idx = index"
                [class.active]="currentPage === idx + 1"
              >
                <a
                  class="modern-page-link"
                  href="javascript:void(0)"
                  (click)="onPageChange(idx + 1)"
                >
                  {{ idx + 1 }}
                </a>
              </li>
              <li
                class="modern-page-item"
                [class.disabled]="currentPage === totalPages"
              >
                <a
                  class="modern-page-link"
                  href="javascript:void(0)"
                  (click)="onPageChange(currentPage + 1)"
                >
                  <i class="fas fa-chevron-right"></i>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
