import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ProfileImageUpdateService {
  private profileImageUpdatedSource = new Subject<void>();
  
  // Observable stream
  profileImageUpdated$ = this.profileImageUpdatedSource.asObservable();

  // Method to trigger profile image update
  notifyProfileImageUpdated() {
    this.profileImageUpdatedSource.next();
  }
}
