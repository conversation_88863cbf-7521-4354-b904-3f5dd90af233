.register-container {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--background-color);
  background-image: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
}

.register-wrapper {
  display: flex;
  width: 90%;
  max-width: 1200px;
  height: 700px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 20px 40px var(--shadow-color);
  position: relative;
}

/* Left Panel - Image */
.register-image-panel {
  flex: 1.2;
  background-image: url('https://images.unsplash.com/photo-1571902943202-507ec2618e8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.85) 0%, rgba(58, 12, 163, 0.85) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.gym-branding {
  text-align: center;
  color: white;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  max-width: 500px;
}

.logo-container {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.gym-branding i {
  font-size: 50px;
  color: white;
}

.gym-branding h1 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.gym-branding p {
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 30px;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  max-width: 300px;
}

.feature {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 10px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.feature i {
  font-size: 20px;
  color: white;
}

.feature span {
  font-size: 16px;
  font-weight: 500;
}

/* Right Panel - Form */
.register-form-panel {
  flex: 0.8;
  background-color: var(--card-bg-color);
  padding: 20px 40px 40px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
}

.register-form-panel::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: var(--primary-color);
  opacity: 0.1;
  border-radius: 50%;
}

.register-form-panel::after {
  content: '';
  position: absolute;
  bottom: -80px;
  left: -80px;
  width: 160px;
  height: 160px;
  background: var(--secondary-color);
  opacity: 0.1;
  border-radius: 50%;
}

.register-form-container {
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  z-index: 1;
}

.register-header {
  margin-top: 0;
  margin-bottom: 15px;
  text-align: center;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
}

.header-icon i {
  font-size: 25px;
  color: white;
}

.register-header h2 {
  color: var(--text-color);
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}

.register-header p {
  color: var(--text-muted);
  font-size: 14px;
  margin-bottom: 0;
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-row {
  display: flex;
  gap: 10px;
}

.form-row .form-group {
  flex: 1;
}

.form-group {
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-color);
  font-weight: 500;
  font-size: 13px;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-group i {
  position: absolute;
  left: 15px;
  color: var(--text-muted);
  font-size: 16px;
}

.form-group input {
  width: 100%;
  padding: 10px 15px 10px 45px;
  border: 1px solid var(--input-border);
  border-radius: 10px;
  font-size: 14px;
  background-color: var(--input-bg);
  color: var(--input-text);
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.form-group input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
  outline: none;
}

.form-group input.is-invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(249, 65, 68, 0.2);
}

.toggle-password {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-password:hover {
  color: var(--primary-color);
}

.error-message {
  color: var(--danger-color);
  font-size: 12px;
  margin-top: 6px;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
}

.login-link {
  color: var(--primary-color);
  font-size: 14px;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}

.login-link:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

.register-button {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  padding: 10px;
  border: none;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 42px;
  box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
  letter-spacing: 0.5px;
  width: 100%;
  margin-top: 10px;
}

.register-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
}

.register-button:active {
  transform: translateY(1px);
}

.register-button:disabled {
  background: linear-gradient(135deg, #a0a0a0 0%, #7a7a7a 100%);
  cursor: not-allowed;
  box-shadow: none;
}

.register-footer {
  margin-top: 15px;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.support {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--text-muted);
  font-size: 14px;
}

.support i {
  color: var(--primary-color);
}

.support a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.support a:hover {
  text-decoration: underline;
}

.register-footer p {
  color: var(--text-muted);
  font-size: 12px;
}

/* Responsive Design */
@media (max-width: 1199.98px) {
  .register-wrapper {
    max-width: 1000px;
  }
}

@media (max-width: 991.98px) {
  .register-wrapper {
    flex-direction: column;
    height: auto;
    max-width: 600px;
  }

  .register-image-panel {
    height: 300px;
  }

  .register-form-panel {
    padding: 40px 30px 60px;
  }

  .features {
    flex-direction: row;
    max-width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .feature {
    width: calc(50% - 10px);
  }
}

@media (max-width: 767.98px) {
  .register-wrapper {
    width: 95%;
  }

  .register-image-panel {
    height: 250px;
  }

  .gym-branding h1 {
    font-size: 32px;
  }

  .gym-branding p {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .features {
    gap: 10px;
  }

  .feature {
    padding: 10px 15px;
  }

  .feature i {
    font-size: 18px;
  }

  .feature span {
    font-size: 14px;
  }
}

@media (max-width: 575.98px) {
  .register-wrapper {
    border-radius: 10px;
  }

  .register-image-panel {
    height: 200px;
  }

  .logo-container {
    width: 80px;
    height: 80px;
  }

  .gym-branding i {
    font-size: 40px;
  }

  .gym-branding h1 {
    font-size: 28px;
  }

  .gym-branding p {
    font-size: 14px;
    margin-bottom: 15px;
  }

  .features {
    flex-direction: column;
  }

  .feature {
    width: 100%;
  }

  .register-form-panel {
    padding: 30px 20px 50px;
  }

  .header-icon {
    width: 60px;
    height: 60px;
  }

  .header-icon i {
    font-size: 25px;
  }

  .register-header h2 {
    font-size: 24px;
  }

  .register-header p {
    font-size: 14px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .register-button {
    height: 40px;
    font-size: 14px;
    margin-top: 5px;
  }
}

/* Dark mode support */
:host-context([data-theme="dark"]) .input-group input,
:host-context([data-theme="dark"]) .input-group select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
}

:host-context([data-theme="dark"]) .register-form-panel {
  background-color: var(--card-bg-color);
}

:host-context([data-theme="dark"]) .register-header h2,
:host-context([data-theme="dark"]) .form-group label {
  color: var(--text-color);
}

:host-context([data-theme="dark"]) .register-footer,
:host-context([data-theme="dark"]) .register-header p {
  color: var(--text-muted);
}

:host-context([data-theme="dark"]) .input-group i,
:host-context([data-theme="dark"]) .input-group .toggle-password {
  color: var(--text-muted);
}

:host-context([data-theme="dark"]) .register-container {
  background-image: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);
}