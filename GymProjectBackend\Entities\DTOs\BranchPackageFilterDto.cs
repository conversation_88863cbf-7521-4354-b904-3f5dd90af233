using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    /// <summary>
    /// İki seviyeli filtreleme için branş ve paket bilgileri
    /// </summary>
    public class BranchPackageFilterDto : IDto
    {
        public string Branch { get; set; }
        public int TotalMembers { get; set; }
        public List<PackageFilterDto> Packages { get; set; }
    }

    /// <summary>
    /// Paket bazlı filtreleme bilgileri
    /// </summary>
    public class PackageFilterDto : IDto
    {
        public int MembershipTypeID { get; set; }
        public string TypeName { get; set; }
        public int Day { get; set; }
        public int MemberCount { get; set; }
        public string PackageGroup { get; set; } // "1 Aylık", "3 Aylık" vs
    }

    /// <summary>
    /// Gelişmiş üye filtreleme parametreleri
    /// </summary>
    public class AdvancedMemberFilterDto : IDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string SearchText { get; set; }
        public int? Gender { get; set; }
        public string Branch { get; set; }
        public int? MembershipTypeID { get; set; }
        public string PackageGroup { get; set; }
        public int? MinDays { get; set; }
        public int? MaxDays { get; set; }
    }

    /// <summary>
    /// Çoklu üyelik görünümü için detaylı üye bilgisi
    /// </summary>
    public class MemberMultipleMembershipDto : IDto
    {
        public int MemberID { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public int Gender { get; set; }
        public List<MembershipDetailViewDto> ActiveMemberships { get; set; }
        public int TotalRemainingDays { get; set; }
        public DateTime? LastUpdateDate { get; set; }
    }

    /// <summary>
    /// Üyelik detay görünümü
    /// </summary>
    public class MembershipDetailViewDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public int Day { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsFrozen { get; set; }
        public decimal Price { get; set; }
    }
}
