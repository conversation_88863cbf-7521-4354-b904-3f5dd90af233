import { Injectable } from '@angular/core';
import { Http<PERSON><PERSON>, Http<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpErrorResponse } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { catchError, filter, take, switchMap } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { TokenResponse } from '../models/refreshToken';
import { Router } from '@angular/router';
import { CompanyContextService } from '../services/company-context.service';
import { ToastrService } from 'ngx-toastr'; // ToastrService import edildi
// DialogService ve DialogType importları kaldırıldı (artık kullanılmıyor)
// import { DialogService } from '../services/dialog.service';
// import { DialogType } from '../models/dialog-type.enum';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
    private isRefreshing = false;
    private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

    constructor(
        private authService: AuthService,
        private companyContextService: CompanyContextService,
        private router: Router,
        private toastr: ToastrService // ToastrService inject edildi
        // private dialogService: DialogService // DialogService inject kaldırıldı
    ) {}

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        // Login ve refresh token endpointleri için token ekleme
        if (request.url.includes('auth/login') || request.url.includes('auth/refresh-token')) {
            return next.handle(request);
        }

        const token = localStorage.getItem('token');

        if (token) {
            request = this.addToken(request, token);
        }

        return next.handle(request).pipe(
            catchError((error: HttpErrorResponse) => { // Tip belirtildi
                if (error instanceof HttpErrorResponse) {
                    switch (error.status) {
                        case 401: // Unauthorized
                            return this.handle401Error(request, next);
                        case 403: // Forbidden
                            return this.handle403Error(error);
                        case 0: // Network error or CORS issue
                            this.showConnectionErrorToast(error); // Toast metodu çağrıldı
                            break;
                        case 500: // Internal Server Error
                        case 502: // Bad Gateway
                        case 503: // Service Unavailable
                        case 504: // Gateway Timeout
                            this.showServerErrorToast(error); // Toast metodu çağrıldı
                            break;
                        case 429: // Too Many Requests
                            // Rate limit hatası için özel diyalog gösterme, sadece console'a logla.
                            // Component seviyesinde toastr ile gösterilecek.
                            console.warn('Rate Limit Hatası (429):', error);
                            break; // Hata yine de fırlatılacak
                        case 400: // Bad Request
                        case 404: // Not Found
                        case 405: // Method Not Allowed
                        case 409: // Conflict
                        case 422: // Unprocessable Entity
                            // Bu hata kodları için özel mesajlar showGenericErrorToast içinde ele alınacak
                            console.error(`HTTP Hatası (${error.status}):`, error);
                            this.showGenericErrorToast(error);
                            break;
                        default:
                            // Diğer HTTP hataları
                            console.error('Beklenmeyen HTTP Hatası:', error);
                            // Eğer hata mesajı varsa onu göster, yoksa genel hata mesajını göster
                            if (error.error && typeof error.error === 'object' && error.error.message) {
                                this.toastr.error(error.error.message, `Hata (${error.status})`);
                            } else {
                                this.showGenericErrorToast(error);
                            }
                            break;
                    }
                } else {
                    // HttpErrorResponse olmayan hatalar (örn. client-side JS hataları)
                    console.error('İstek sırasında beklenmeyen bir hata oluştu:', error);

                    // Geliştirme ortamında JavaScript hatalarını göster
                    // Üretim ortamında bu hataları göstermek istemeyebilirsiniz
                    if (typeof error === 'object' && error !== null && 'message' in error) {
                        const jsError = error as { message: string };
                        this.toastr.error(
                            `Uygulama hatası: ${jsError.message}`,
                            'JavaScript Hatası'
                        );
                    } else {
                        // Diğer tüm hatalar için genel hata mesajı
                        this.showGenericErrorToast(error);
                    }
                }
                return throwError(() => error); // Hata her zaman fırlatılır
            })
        );
    }

    private addToken(request: HttpRequest<any>, token: string) {
        return request.clone({
            setHeaders: {
                Authorization: `Bearer ${token}`
            }
        });
    }

    private handle401Error(request: HttpRequest<any>, next: HttpHandler) {
        if (!this.isRefreshing) {
            this.isRefreshing = true;
            this.refreshTokenSubject.next(null);

            return this.authService.refreshToken().pipe(
                switchMap((response: TokenResponse) => {
                    this.isRefreshing = false;
                    if (response.success && response.data) {
                        this.refreshTokenSubject.next(response.data.token);
                        return next.handle(this.addToken(request, response.data.token));
                    }
                    this.authService.logout(); // Refresh token başarısızsa logout yap
                    return throwError(() => 'Token refresh failed');
                }),
                catchError((error) => {
                    this.isRefreshing = false;
                    this.authService.logout(); // Refresh token sırasında hata olursa logout yap
                    return throwError(() => error);
                })
            );
        }

        // Eğer token zaten yenileniyorsa, yeni token'ı bekle ve isteği onunla tekrarla
        return this.refreshTokenSubject.pipe(
            filter(token => token != null),
            take(1),
            switchMap(token => {
                return next.handle(this.addToken(request, token));
            })
        );
    }

    private handle403Error(error: HttpErrorResponse) {
        // Yetkilendirme hatası - kullanıcı bu kaynağa erişim izni yok
        console.error('Yetkilendirme hatası (403):', error);

        // Kullanıcıya bilgi ver ve uygun sayfaya yönlendir
        this.router.navigate(['/unauthorized'], {
            queryParams: {
                // returnUrl: this.router.url, // Gerekirse geri dönüş URL'si eklenebilir
                message: 'Bu işlemi gerçekleştirmek için yetkiniz bulunmamaktadır.'
            }
        });

        return throwError(() => error);
    }

    private showConnectionErrorToast(error: HttpErrorResponse): void {
        console.error('Bağlantı Hatası (Status 0):', error);
        this.toastr.error(
            'Sunucuya ulaşılamıyor. Lütfen internet bağlantınızı kontrol edin veya daha sonra tekrar deneyin.',
            'Bağlantı Hatası'
        );
    }

    private showServerErrorToast(error: HttpErrorResponse): void {
        console.error(`Sunucu Hatası (${error.status}):`, error);
        this.toastr.error(
            `Sunucuda bir hata oluştu (Kod: ${error.status}). Lütfen daha sonra tekrar deneyin veya sistem yöneticisine başvurun.`,
            'Sunucu Hatası'
        );
    }

    private showGenericErrorToast(error: any): void {
        // Hatanın detayını logla
        console.error('Genel Hata:', error);

        // Hata mesajını belirle
        let errorMessage = '';
        let errorTitle = 'Hata';

        // HTTP hatası ise
        if (error instanceof HttpErrorResponse) {
            // Sunucudan gelen özel hata mesajı varsa onu kullan
            if (error.error && error.error.message) {
                errorMessage = error.error.message;
            }
            // Validation hatası varsa
            else if (error.error && error.error.Errors && error.error.Errors.length > 0) {
                // İlk validation hatasını göster
                errorMessage = error.error.Errors[0].ErrorMessage || 'Doğrulama hatası oluştu.';
                errorTitle = 'Doğrulama Hatası';
            }
            // HTTP durum koduna göre özel mesajlar
            else {
                switch (error.status) {
                    case 400: // Bad Request
                        errorMessage = 'Geçersiz istek formatı. Lütfen girdiğiniz bilgileri kontrol edin.';
                        break;
                    case 404: // Not Found
                        errorMessage = 'İstenen kaynak bulunamadı.';
                        break;
                    case 405: // Method Not Allowed
                        errorMessage = 'Bu işlem desteklenmiyor.';
                        break;
                    case 409: // Conflict
                        errorMessage = 'Bu işlem mevcut verilerle çakışıyor.';
                        break;
                    case 422: // Unprocessable Entity
                        errorMessage = 'Gönderilen veriler işlenemedi. Lütfen girdiğiniz bilgileri kontrol edin.';
                        break;
                    default:
                        // Diğer durumlarda genel mesaj
                        errorMessage = 'İşlem sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
                }
            }
        }
        // JavaScript hatası ise
        else if (typeof error === 'object' && error !== null && 'message' in error) {
            const jsError = error as { message: string };
            errorMessage = 'Uygulama hatası: ' + jsError.message;
        }
        // Diğer hatalar
        else {
            errorMessage = 'İşlem sırasında beklenmeyen bir hata oluştu.';
        }

        // Hata mesajı boş değilse göster
        if (errorMessage) {
            this.toastr.error(errorMessage, errorTitle);
        }
    }
}
