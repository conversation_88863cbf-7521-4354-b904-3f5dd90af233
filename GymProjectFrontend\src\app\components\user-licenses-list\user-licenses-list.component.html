<div class="container mt-4">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h3>Kullanıcı Lisansları</h3>
        <button mat-raised-button color="primary" (click)="openPurchaseDialog()">
          <i class="fas fa-plus me-2"></i>Yeni Lisans Satın Al
        </button>
      </div>
  
      <div class="card-body">
        <div *ngIf="isLoading" class="d-flex justify-content-center">
          <app-loading-spinner></app-loading-spinner>
        </div>
  
        <div *ngIf="!isLoading && userLicenses.length === 0" class="alert alert-info">
          Hen<PERSON>z kullanıcı lisansı bulunmamaktadır.
        </div>
  
        <div *ngIf="!isLoading && userLicenses.length > 0" class="table-responsive">
          <table mat-table [dataSource]="userLicenses" class="w-100">
            <!-- User Name Column -->
            <ng-container matColumnDef="userName">
              <th mat-header-cell *matHeaderCellDef>Kullanıcı</th>
              <td mat-cell *matCellDef="let item">
                {{ item.userName }}<br>
                <small class="text-muted">{{ item.userEmail }}</small>
              </td>
            </ng-container>
  
            <!-- Package Name Column -->
            <ng-container matColumnDef="packageName">
              <th mat-header-cell *matHeaderCellDef>Paket</th>
              <td mat-cell *matCellDef="let item">{{ item.packageName }}</td>
            </ng-container>
  
            <!-- Role Column -->
            <ng-container matColumnDef="role">
              <th mat-header-cell *matHeaderCellDef>Rol</th>
              <td mat-cell *matCellDef="let item">{{ item.role }}</td>
            </ng-container>
  
            <!-- Start Date Column -->
            <ng-container matColumnDef="startDate">
              <th mat-header-cell *matHeaderCellDef>Başlangıç Tarihi</th>
              <td mat-cell *matCellDef="let item">{{ item.startDate | date:'dd/MM/yyyy' }}</td>
            </ng-container>
  
            <!-- End Date Column -->
            <ng-container matColumnDef="endDate">
              <th mat-header-cell *matHeaderCellDef>Bitiş Tarihi</th>
              <td mat-cell *matCellDef="let item">{{ item.endDate | date:'dd/MM/yyyy' }}</td>
            </ng-container>
  
            <!-- Remaining Days Column -->
            <ng-container matColumnDef="remainingDays">
              <th mat-header-cell *matHeaderCellDef>Kalan Gün</th>
              <td mat-cell *matCellDef="let item" [ngClass]="getRemainingDaysClass(item.remainingDays)">
                {{ item.remainingDays }} gün
              </td>
            </ng-container>
  
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>İşlemler</th>
              <td mat-cell *matCellDef="let item">
                <button mat-icon-button color="primary" (click)="openExtendDialog(item)" matTooltip="Uzat">
                  <i class="fas fa-calendar-plus"></i>
                </button>
                <button mat-icon-button color="warn" (click)="revokeLicense(item.userLicenseID, item.userName)" matTooltip="İptal Et">
                  <i class="fas fa-times-circle"></i>
                </button>
              </td>
            </ng-container>
  
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </div>
      </div>
    </div>
  </div>