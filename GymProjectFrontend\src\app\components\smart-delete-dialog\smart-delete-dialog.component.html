<div class="smart-delete-dialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <app-loading-spinner></app-loading-spinner>
    <p class="text-center mt-3">Üyelik bilgileri yükleniyor...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && !showResult" class="dialog-content">
    <!-- Header -->
    <div class="dialog-header">
      <h4 class="dialog-title">
        <fa-icon [icon]="faExclamationTriangle" class="text-warning me-2"></fa-icon>
        Üyelik Silme İşlemi
      </h4>
      <p class="dialog-subtitle" *ngIf="memberData">
        <strong>{{ memberData.memberName }}</strong> - {{ memberData.phoneNumber }}
      </p>
    </div>

    <!-- Member Info -->
    <div class="member-info-card" *ngIf="memberData">
      <div class="info-row">
        <span class="info-label">Toplam Aktif Üyelik:</span>
        <span class="info-value">{{ memberData.totalActiveMemberships }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">Toplam Ödenen Tutar:</span>
        <span class="info-value text-primary">{{ formatCurrency(memberData.totalPaidAmount) }}</span>
      </div>
      <div class="info-row" *ngIf="memberData.hasMultipleMemberships">
        <span class="info-label">Çoklu Üyelik:</span>
        <span class="modern-badge modern-badge-warning">Evet</span>
      </div>
    </div>

    <!-- Membership Selection -->
    <div class="membership-selection" *ngIf="memberData">
      <h5 class="section-title">
        <fa-icon [icon]="faInfoCircle" class="text-info me-2"></fa-icon>
        Silinecek Üyelikleri Seçin
      </h5>

      <!-- Select All Option -->
      <div class="select-all-option" *ngIf="memberData.hasMultipleMemberships">
        <div class="modern-checkbox">
          <input
            type="checkbox"
            id="selectAll"
            class="modern-checkbox-input"
            formControlName="deleteAllMemberships"
            (change)="toggleAllMemberships()">
          <label for="selectAll" class="modern-checkbox-label">
            <span class="checkbox-icon"></span>
            Tüm üyelikleri sil
          </label>
        </div>
      </div>

      <!-- Membership List -->
      <div class="membership-list">
        <div 
          *ngFor="let membership of memberData.activeMemberships" 
          class="membership-item"
          [class.selected]="isMembershipSelected(membership.membershipID)"
          [class.warning]="membership.deleteWarning"
          (click)="toggleMembershipSelection(membership.membershipID)">
          
          <div class="membership-checkbox">
            <input
              type="checkbox"
              [id]="'membership-' + membership.membershipID"
              class="modern-checkbox-input"
              [checked]="isMembershipSelected(membership.membershipID)"
              (change)="toggleMembershipSelection(membership.membershipID)"
              (click)="$event.stopPropagation()">
            <label [for]="'membership-' + membership.membershipID" class="modern-checkbox-label">
              <span class="checkbox-icon"></span>
            </label>
          </div>

          <div class="membership-details">
            <div class="membership-header">
              <h6 class="membership-title">
                {{ membership.branch }} - {{ membership.typeName }}
              </h6>
              <span class="modern-badge modern-badge-info">{{ membership.day }} gün</span>
            </div>

            <div class="membership-info">
              <div class="info-grid">
                <div class="info-item">
                  <small class="info-label">Başlangıç:</small>
                  <span class="info-value">{{ membership.startDate | date:'dd.MM.yyyy' }}</span>
                </div>
                <div class="info-item">
                  <small class="info-label">Bitiş:</small>
                  <span class="info-value">{{ membership.endDate | date:'dd.MM.yyyy' }}</span>
                </div>
                <div class="info-item">
                  <small class="info-label">Kalan Gün:</small>
                  <span class="info-value" [ngClass]="getRemainingDaysClass(membership.remainingDays)">
                    {{ membership.remainingDays }} gün
                  </span>
                </div>
                <div class="info-item">
                  <small class="info-label">Ödenen Tutar:</small>
                  <span class="info-value text-success">{{ formatCurrency(membership.totalPaidAmount) }}</span>
                </div>
              </div>

              <!-- Freeze Status -->
              <div class="freeze-status" *ngIf="membership.isFrozen">
                <span class="modern-badge modern-badge-secondary">
                  <i class="fas fa-snowflake me-1"></i>
                  Dondurulmuş
                </span>
                <small *ngIf="membership.freezeEndDate">
                  Bitiş: {{ membership.freezeEndDate | date:'dd.MM.yyyy' }}
                </small>
              </div>

              <!-- Payment Info -->
              <div class="payment-info" *ngIf="membership.paymentCount > 0">
                <small class="text-muted">
                  {{ membership.paymentCount }} ödeme - {{ membership.paymentMethods }}
                  <span *ngIf="membership.lastPaymentDate">
                    (Son: {{ membership.lastPaymentDate | date:'dd.MM.yyyy' }})
                  </span>
                </small>
              </div>

              <!-- Warning -->
              <div class="delete-warning" *ngIf="membership.deleteWarning">
                <fa-icon [icon]="faExclamationTriangle" class="text-warning me-1"></fa-icon>
                <small class="text-warning">{{ membership.deleteWarning }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Form -->
    <form [formGroup]="deleteForm" class="delete-form">
      <!-- Delete Reason -->
      <div class="form-group">
        <label for="deleteReason" class="form-label required">Silme Nedeni</label>
        <textarea 
          id="deleteReason"
          class="modern-form-control"
          formControlName="deleteReason"
          rows="3"
          placeholder="Üyelik silme nedeninizi açıklayın..."></textarea>
      </div>

      <!-- Payment Loss Confirmation -->
      <div class="form-group" *ngIf="getTotalSelectedPaymentAmount() > 0">
        <div class="payment-loss-warning">
          <fa-icon [icon]="faExclamationTriangle" class="text-danger me-2"></fa-icon>
          <strong>Uyarı:</strong> 
          {{ formatCurrency(getTotalSelectedPaymentAmount()) }} tutarındaki ödeme kaybolacak.
        </div>
        <div class="modern-checkbox mt-2">
          <input 
            type="checkbox" 
            id="confirmPaymentLoss"
            class="modern-checkbox-input"
            formControlName="confirmPaymentLoss">
          <label for="confirmPaymentLoss" class="modern-checkbox-label">
            <span class="checkbox-icon"></span>
            Ödeme kaybını onaylıyorum
          </label>
        </div>
      </div>
    </form>

    <!-- Action Buttons -->
    <div class="dialog-actions">
      <button 
        type="button" 
        class="modern-btn modern-btn-secondary"
        (click)="closeDialog()"
        [disabled]="isDeleting">
        İptal
      </button>
      <button 
        type="button" 
        class="modern-btn modern-btn-danger"
        (click)="performDelete()"
        [disabled]="isDeleting || selectedMembershipIds.length === 0">
        <fa-icon [icon]="faTrashAlt" class="me-1"></fa-icon>
        <span *ngIf="!isDeleting">Sil ({{ selectedMembershipIds.length }})</span>
        <span *ngIf="isDeleting">Siliniyor...</span>
      </button>
    </div>
  </div>

  <!-- Result Display -->
  <div *ngIf="showResult && deleteResult" class="result-content">
    <div class="result-header">
      <fa-icon 
        [icon]="deleteResult.success ? faCheckCircle : faExclamationTriangle" 
        [class]="deleteResult.success ? 'text-success' : 'text-danger'"
        class="result-icon"></fa-icon>
      <h4 class="result-title">{{ deleteResult.success ? 'İşlem Başarılı' : 'İşlem Başarısız' }}</h4>
      <p class="result-message">{{ deleteResult.message }}</p>
    </div>

    <div class="result-details" *ngIf="deleteResult.success">
      <div class="detail-item">
        <span class="detail-label">Silinen Üyelik Sayısı:</span>
        <span class="detail-value">{{ deleteResult.deletedMembershipsCount }}</span>
      </div>
      <div class="detail-item" *ngIf="deleteResult.refundableAmount > 0">
        <span class="detail-label">İade Edilebilir Tutar:</span>
        <span class="detail-value text-success">{{ formatCurrency(deleteResult.refundableAmount) }}</span>
      </div>
      
      <!-- Deleted Memberships -->
      <div class="deleted-memberships" *ngIf="deleteResult.deletedMembershipDetails.length > 0">
        <h6>Silinen Üyelikler:</h6>
        <ul class="deleted-list">
          <li *ngFor="let detail of deleteResult.deletedMembershipDetails">{{ detail }}</li>
        </ul>
      </div>

      <!-- Warnings -->
      <div class="warnings" *ngIf="deleteResult.warnings.length > 0">
        <h6 class="text-warning">Uyarılar:</h6>
        <ul class="warning-list">
          <li *ngFor="let warning of deleteResult.warnings" class="text-warning">{{ warning }}</li>
        </ul>
      </div>

      <!-- Manager Approval -->
      <div class="manager-approval" *ngIf="deleteResult.requiresManagerApproval">
        <div class="modern-alert modern-alert-warning">
          <fa-icon [icon]="faInfoCircle" class="me-2"></fa-icon>
          Bu işlem için yönetici onayı gerekebilir.
        </div>
      </div>
    </div>

    <div class="result-actions">
      <button 
        type="button" 
        class="modern-btn modern-btn-primary"
        (click)="closeDialog()">
        Tamam
      </button>
    </div>
  </div>
</div>
