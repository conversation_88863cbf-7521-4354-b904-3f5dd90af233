/* Exercise Selection Modal Specific Styles */

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  margin: 0;
  font-weight: 600;
  color: var(--text-primary);
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-speed) var(--transition-timing);
}

.btn-close:hover {
  background-color: var(--danger-light);
  color: var(--danger);
}

.modal-body {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.filters-section {
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.exercises-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
}

.exercise-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-timing);
  position: relative;
}

.exercise-item:last-child {
  border-bottom: none;
}

.exercise-item:hover {
  background-color: var(--bg-secondary);
}

.exercise-item.selected {
  background-color: var(--primary-light);
  border-color: var(--primary);
}

.exercise-content {
  flex: 1;
}

.exercise-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.exercise-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-primary);
}

.exercise-badges {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.exercise-details {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.exercise-category {
  font-weight: 500;
  color: var(--primary);
  margin-bottom: 0.25rem;
}

.exercise-description {
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.exercise-muscles,
.exercise-equipment {
  margin-bottom: 0.25rem;
}

.exercise-select-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 24px;
  height: 24px;
  background-color: var(--primary);
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.pagination-section {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.pagination .page-link {
  color: var(--primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  padding: 0.5rem 0.75rem;
  margin: 0 0.125rem;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-speed) var(--transition-timing);
}

.pagination .page-link:hover {
  color: var(--white);
  background-color: var(--primary);
  border-color: var(--primary);
  transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
  color: var(--white);
  background-color: var(--primary);
  border-color: var(--primary);
}

.pagination .page-item.disabled .page-link {
  color: var(--text-secondary);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  cursor: not-allowed;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Badge Styles */
.modern-badge-secondary {
  background-color: var(--secondary-light);
  color: var(--secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }
  
  .exercise-item {
    padding: 0.75rem;
  }
  
  .exercise-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .exercise-badges {
    align-self: flex-start;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .modal-footer .modern-btn {
    width: 100%;
  }
  
  .exercises-list {
    max-height: 300px;
  }
}

/* Dark mode specific adjustments */
[data-theme="dark"] .modal-header {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .modal-footer {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .filters-section {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .exercises-list {
  border-color: var(--border-color);
}

[data-theme="dark"] .exercise-item {
  border-color: var(--border-color);
}

[data-theme="dark"] .exercise-item:hover {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .exercise-item.selected {
  background-color: var(--primary-light);
  border-color: var(--primary);
}

[data-theme="dark"] .btn-close:hover {
  background-color: var(--danger-light);
  color: var(--danger);
}

[data-theme="dark"] .pagination .page-link {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .pagination .page-link:hover {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

[data-theme="dark"] .pagination .page-item.disabled .page-link {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}
