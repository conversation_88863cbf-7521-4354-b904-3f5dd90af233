import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, On<PERSON><PERSON>roy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { MemberService } from '../../services/member.service';
import { MembershipType } from '../../models/membershipType';
import { MembershipTypeService } from '../../services/membership-type.service';
import { MatDialog } from '@angular/material/dialog';
import { MembershipUpdateComponent } from '../crud/membership-update/membership-update.component';
import { faEdit, faSnowflake, faTrashAlt, faFilter, faLayerGroup } from '@fortawesome/free-solid-svg-icons';
import { MembershipService } from '../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { MemberFilter } from '../../models/memberFilter';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { FreezeMembershipDialogComponent } from '../freeze-membership-dialog/freeze-membership-dialog.component';
import { DialogService } from '../../services/dialog.service';
import { Chart, ChartConfiguration, ChartType } from 'chart.js';
import { AdvancedMemberFilterService } from '../../services/advanced-member-filter.service';
import { MembershipDeleteService } from '../../services/membership-delete.service';
import { BranchPackageFilter, PackageFilter } from '../../models/branch-package-filter';
import { SmartDeleteDialogComponent } from '../smart-delete-dialog/smart-delete-dialog.component';

// YENİ BASIT MODELLER
interface PackageData {
  id: number;
  name: string;
  duration: number;
  memberCount: number;
}

interface BranchData {
  name: string;
  totalMembers: number;
  packages: PackageData[];
}

@Component({
    selector: 'app-member-filter',
    templateUrl: './member-filter.component.html',
    styleUrls: ['./member-filter.component.css'],
    standalone: false
})
export class MemberFilterComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('genderChart') genderChartRef: ElementRef;
  genderChart: Chart;
  members: MemberFilter[] = [];
  activeMembersCount: number = 0;
  memberFilterText: string = '';
  private searchTextSubject = new Subject<string>();
  genderFilter: string = '';
  branchFilter: string = '';
  membershipTypes: MembershipType[] = [];
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faSnowflake = faSnowflake;
  faFilter = faFilter;
  faLayerGroup = faLayerGroup;
  isLoading: boolean = false;
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  totalActiveMembers: number = 0;

  // YENİ BASIT FİLTRELEME SİSTEMİ
  currentFilter = {
    branchName: '',
    packageId: null as number | null,
    packageName: ''
  };

  activeFilter = {
    isActive: false,
    displayText: ''
  };

  branches: BranchData[] = [];
  expandedBranch: string = '';

  // Çoklu üyelik takibi için
  multipleMembershipsMap: Map<number, boolean> = new Map();

  // Spam koruması için
  isProcessing: boolean = false;
  private lastClickTime: number = 0;
  private readonly CLICK_DEBOUNCE_TIME = 300; // 300ms

  genderCounts = {
    all: 0,
    male: 0,
    female: 0
  };
  branchCounts: { [key: string]: number } = {};

  constructor(
    private memberService: MemberService,
    private membershipTypeService: MembershipTypeService,
    private membershipService: MembershipService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService,
    private advancedFilterService: AdvancedMemberFilterService,
    private membershipDeleteService: MembershipDeleteService
  ) {
    this.searchTextSubject.pipe(
      debounceTime(750),
      distinctUntilChanged()
    ).subscribe(searchText => {
      this.memberFilterText = searchText;
      this.currentPage = 1;
      this.loadMembers();
    });
  }

  ngOnInit(): void {
    this.loadBranchData();
    this.loadMultipleMembershipsData();
    this.loadMembers();
    this.getTotalActiveMembers();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.initGenderChart();
    }, 500);
  }

  ngOnDestroy(): void {
    this.searchTextSubject.complete();
    if (this.genderChart) {
      this.genderChart.destroy();
    }
  }

  initGenderChart(): void {
    const canvas = document.getElementById('genderChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    if (this.genderChart) {
      this.genderChart.destroy();
    }

    const chartData = {
      labels: ['Erkek', 'Kadın'],
      datasets: [{
        data: [this.genderCounts.male, this.genderCounts.female],
      backgroundColor: [
        'rgba(67, 97, 238, 0.7)',  // Erkek - Mavi
        'rgba(255, 105, 180, 0.7)'   // Kadın - Pembe
      ],
      borderColor: [
        'rgb(67, 97, 238)',
        'rgb(255, 105, 180)'
        ],
        borderWidth: 1,
        hoverOffset: 4
      }]
    };

    const config: ChartConfiguration = {
      type: 'doughnut' as ChartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              pointStyle: 'circle',
              font: {
                size: 12
              }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw as number;
                const total = (context.dataset.data as number[]).reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        },
        animation: {
          duration: 1000
        },
        layout: {
          padding: {
            top: 10,
            bottom: 20
          }
        }
      }
    };

    this.genderChart = new Chart(ctx, config);
  }

  searchTextChanged(text: string) {
    this.searchTextSubject.next(text);
  }

  // ===== YENİ BASIT FİLTRELEME SİSTEMİ =====

  /**
   * Branş verilerini yükler
   */
  loadBranchData(): void {
    this.advancedFilterService.getBranchPackageFilters().subscribe({
      next: (response) => {
        if (response.success) {
          this.branches = response.data.map(branch => ({
            name: branch.branch,
            totalMembers: branch.totalMembers,
            packages: branch.packages.map(pkg => ({
              id: pkg.membershipTypeID,
              name: pkg.typeName,
              duration: pkg.day,
              memberCount: pkg.memberCount
            }))
          }));
        }
      },
      error: (error) => {
        console.error('Error loading branch data:', error);
      }
    });
  }

  /**
   * Filtreleme değişikliği - SCROLL SORUNU YOK!
   */
  private applyFilter(): void {
    this.currentPage = 1;
    this.updateActiveFilterDisplay();

    // Scroll'u HEMEN yap - DOM güncellemesini bekleme
    this.scrollToTopImmediate();

    // Sonra verileri yükle
    this.loadMembers();
    this.getTotalActiveMembers();
  }

  /**
   * Aktif filtre görünümünü günceller
   */
  private updateActiveFilterDisplay(): void {
    if (!this.currentFilter.branchName) {
      this.activeFilter.isActive = false;
      this.activeFilter.displayText = '';
    } else if (this.currentFilter.packageId) {
      this.activeFilter.isActive = true;
      this.activeFilter.displayText = `${this.currentFilter.branchName} - ${this.currentFilter.packageName}`;
    } else {
      this.activeFilter.isActive = true;
      this.activeFilter.displayText = `${this.currentFilter.branchName} - Tüm Paketler`;
    }
  }

  /**
   * HEMEN scroll yap - hiç bekleme!
   */
  private scrollToTopImmediate(): void {
    // Hemen instant scroll
    window.scrollTo({ top: 0, behavior: 'instant' });

    // Hiçbir şey bekleme, hemen tekrar scroll
    window.scrollTo({ top: 0, behavior: 'instant' });
  }

  // ===== FİLTRELEME METODLARI =====

  /**
   * Cinsiyet filtresi değiştiğinde
   */
  onGenderChange(): void {
    this.applyFilter();
  }

  /**
   * Tüm branşları seç
   */
  selectAllBranches(): void {
    if (!this.canProcessClick()) return;

    this.startProcessing();
    this.currentFilter.branchName = '';
    this.currentFilter.packageId = null;
    this.currentFilter.packageName = '';
    this.expandedBranch = '';
    this.applyFilter();
    this.endProcessing();
  }

  /**
   * Branş seç
   */
  selectBranch(branchName: string): void {
    if (!this.canProcessClick()) return;

    this.startProcessing();
    this.currentFilter.branchName = branchName;
    this.currentFilter.packageId = null;
    this.currentFilter.packageName = '';
    this.applyFilter();
    this.endProcessing();
  }

  /**
   * Paket seç
   */
  selectPackage(branchName: string, packageId: number, packageName: string): void {
    if (!this.canProcessClick()) return;

    this.startProcessing();
    this.currentFilter.branchName = branchName;
    this.currentFilter.packageId = packageId;
    this.currentFilter.packageName = packageName;
    this.applyFilter();
    this.endProcessing();
  }

  /**
   * Branş dropdown aç/kapat - AGRESİF SCROLL KONTROLÜ
   */
  toggleBranchDropdown(branchName: string, event: Event): void {
    event.stopPropagation(); // Branş seçimini engelle
    event.preventDefault(); // Default davranışı engelle

    // Mevcut scroll pozisyonunu kaydet
    const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
    console.log('Dropdown toggle - Current scroll:', currentScrollTop);

    // Dropdown durumunu değiştir
    if (this.expandedBranch === branchName) {
      this.expandedBranch = '';
    } else {
      this.expandedBranch = branchName;
    }

    // HEMEN scroll pozisyonunu koru
    window.scrollTo({ top: currentScrollTop, behavior: 'instant' });

    // DOM güncellemesini bekle ve tekrar koru
    setTimeout(() => {
      window.scrollTo({ top: currentScrollTop, behavior: 'instant' });
      console.log('Dropdown - First timeout scroll:', currentScrollTop);
    }, 0);

    // Ekstra güvenlik - daha fazla bekle
    setTimeout(() => {
      window.scrollTo({ top: currentScrollTop, behavior: 'instant' });
      console.log('Dropdown - Second timeout scroll:', currentScrollTop);
    }, 10);

    // Son güvenlik
    setTimeout(() => {
      window.scrollTo({ top: currentScrollTop, behavior: 'instant' });
      console.log('Dropdown - Final timeout scroll:', currentScrollTop);
    }, 50);

    // Çok geç gelen scroll'ları da engelle
    setTimeout(() => {
      window.scrollTo({ top: currentScrollTop, behavior: 'instant' });
    }, 100);
  }

  /**
   * Tüm filtreleri temizle
   */
  clearAllFilters(): void {
    if (!this.canProcessClick()) return;

    this.startProcessing();
    this.currentFilter.branchName = '';
    this.currentFilter.packageId = null;
    this.currentFilter.packageName = '';
    this.genderFilter = '';
    this.expandedBranch = '';
    this.applyFilter();
    this.endProcessing();
  }









  /**
   * Üyenin çoklu üyeliği olup olmadığını kontrol eder
   */
  hasMultipleMemberships(memberId: number): boolean {
    return this.multipleMembershipsMap.get(memberId) || false;
  }

  /**
   * Çoklu üyelik bilgilerini yükler
   */
  loadMultipleMembershipsData(): void {
    this.advancedFilterService.getMembersWithMultipleMemberships().subscribe({
      next: (response) => {
        if (response.success) {
          // Çoklu üyeliği olan üyeleri map'e ekle
          this.multipleMembershipsMap.clear();
          response.data.forEach(member => {
            if (member.activeMemberships.length > 1) {
              this.multipleMembershipsMap.set(member.memberID, true);
            }
          });
        }
      },
      error: (error) => {
        console.error('Error loading multiple memberships data:', error);
      }
    });
  }



  /**
   * Spam koruması kontrolü
   */
  private canProcessClick(): boolean {
    const now = Date.now();
    if (this.isProcessing || (now - this.lastClickTime) < this.CLICK_DEBOUNCE_TIME) {
      return false;
    }
    this.lastClickTime = now;
    return true;
  }

  /**
   * İşlem başlatma
   */
  private startProcessing(): void {
    this.isProcessing = true;
  }

  /**
   * İşlem bitirme
   */
  private endProcessing(): void {
    setTimeout(() => {
      this.isProcessing = false;
    }, this.CLICK_DEBOUNCE_TIME);
  }




  openFreezeDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(FreezeMembershipDialogComponent, {
      width: '400px',
      data: { 
        memberName: member.name,
        membershipID: member.membershipID
      }
    });

    dialogRef.afterClosed().subscribe(freezeDays => {
      if (freezeDays) {
        this.membershipService.freezeMembership(member.membershipID, freezeDays).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Üyelik başarıyla donduruldu');
              this.loadMembers();
            } else {
              this.toastrService.error(response.message);
            }
          },
          error: (error) => {
            this.toastrService.error('Üyelik dondurulurken bir hata oluştu');
          }
        });
      }
    });
  }
  getTotalActiveMembers() {
    this.memberService.getTotalActiveMembers().subscribe({
      next: (response) => {
        if (response.success) {
          this.totalActiveMembers = response.data;
          this.activeMembersCount = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching total members:', error);
      },
    });
  }

  loadMembers() {
    this.isLoading = true;
    const gender = this.genderFilter ? parseInt(this.genderFilter) : undefined;

    // YENİ BASIT FİLTRELEME SİSTEMİ
    this.memberService
      .getMemberDetailsPaginated(
        this.currentPage,
        this.memberFilterText,
        gender,
        this.currentFilter.branchName || undefined,
        this.currentFilter.packageId || undefined,
        undefined // packageGroup artık kullanmıyoruz
      )
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.members = response.data.data;
            this.totalPages = response.data.totalPages;
            this.totalItems = response.data.totalCount;
            this.calculateActiveMembersCount();
            this.calculateFilterCounts();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching members:', error);
          this.toastrService.error(
            'Üyeler yüklenirken bir hata oluştu.',
            'Hata'
          );
          this.isLoading = false;
        },
      });
  }

  calculateFilterCounts() {
    this.genderCounts.all = this.totalItems;
  
    this.memberService.getActiveMemberCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.genderCounts.male = response.data['male'];
          this.genderCounts.female = response.data['female'];
          
          // Cinsiyet grafiğini güncelle
          if (this.genderChart) {
            this.genderChart.data.datasets[0].data = [
              this.genderCounts.male, 
              this.genderCounts.female
            ];
            this.genderChart.update();
          } else {
            this.initGenderChart();
          }
        }
      },
      error: (error) => {
        console.error('Error fetching gender counts:', error);
      }
    });
  
    this.memberService.getBranchCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.branchCounts = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching branch counts:', error);
      }
    });
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadMembers();
    }
  }

  getBranches() {
    this.membershipTypeService.getMembershipTypes().subscribe((response) => {
      this.membershipTypes = this.getUniqueBranches(response.data);
    });
  }

  getUniqueBranches(membershipTypes: MembershipType[]): MembershipType[] {
    const uniqueBranches: MembershipType[] = [];
    const branchMap = new Map<string, boolean>();

    membershipTypes.forEach((type) => {
      if (!branchMap.has(type.branch)) {
        branchMap.set(type.branch, true);
        uniqueBranches.push(type);
      }
    });

    return uniqueBranches;
  }

  calculateActiveMembersCount() {
    this.activeMembersCount = this.members.filter((member) => {
      return member.remainingDays >= 0;
    }).length;
  }

  openUpdateDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(MembershipUpdateComponent, {
      width: '400px',
      data: {
        membershipID: member.membershipID,
        memberID: member.memberID,
        membershipTypeID: member.membershipTypeID,
        startDate: member.startDate,
        endDate: member.endDate,
        name: member.name,
        branch: member.branch,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadMembers();
      }
    });
  }

  deleteMember(member: MemberFilter) {
    // Akıllı silme dialog'unu aç
    const dialogRef = this.dialog.open(SmartDeleteDialogComponent, {
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      data: { memberId: member.memberID },
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Silme işlemi başarılı, listeyi yenile
        this.loadMembers();
        this.getTotalActiveMembers();
        this.loadBranchData(); // Filtreleme verilerini de güncelle
      }
    });
  }
}