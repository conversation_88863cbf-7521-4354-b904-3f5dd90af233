import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, On<PERSON><PERSON>roy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { MemberService } from '../../services/member.service';
import { MembershipType } from '../../models/membershipType';
import { MembershipTypeService } from '../../services/membership-type.service';
import { MatDialog } from '@angular/material/dialog';
import { MembershipUpdateComponent } from '../crud/membership-update/membership-update.component';
import { faEdit, faSnowflake, faTrashAlt, faFilter, faLayerGroup } from '@fortawesome/free-solid-svg-icons';
import { MembershipService } from '../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { MemberFilter } from '../../models/memberFilter';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { FreezeMembershipDialogComponent } from '../freeze-membership-dialog/freeze-membership-dialog.component';
import { DialogService } from '../../services/dialog.service';
import { Chart, ChartConfiguration, ChartType } from 'chart.js';
import { AdvancedMemberFilterService } from '../../services/advanced-member-filter.service';
import { MembershipDeleteService } from '../../services/membership-delete.service';
import { BranchPackageFilter, PackageFilter } from '../../models/branch-package-filter';
import { SmartDeleteDialogComponent } from '../smart-delete-dialog/smart-delete-dialog.component';

@Component({
    selector: 'app-member-filter',
    templateUrl: './member-filter.component.html',
    styleUrls: ['./member-filter.component.css'],
    standalone: false
})
export class MemberFilterComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('genderChart') genderChartRef: ElementRef;
  genderChart: Chart;
  members: MemberFilter[] = [];
  activeMembersCount: number = 0;
  memberFilterText: string = '';
  private searchTextSubject = new Subject<string>();
  genderFilter: string = '';
  branchFilter: string = '';
  membershipTypes: MembershipType[] = [];
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faSnowflake = faSnowflake;
  faFilter = faFilter;
  faLayerGroup = faLayerGroup;
  isLoading: boolean = false;
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  totalActiveMembers: number = 0;

  // İki seviyeli filtreleme için yeni özellikler
  branchPackageFilters: BranchPackageFilter[] = [];
  selectedBranch: string = '';
  selectedPackageGroup: string = '';
  selectedMembershipTypeID: number | null = null;
  availablePackages: PackageFilter[] = [];
  showPackageFilters: boolean = false;

  // Çoklu üyelik takibi için
  multipleMembershipsMap: Map<number, boolean> = new Map();

  // Accordion kontrolü için
  expandedBranch: string = '';

  genderCounts = {
    all: 0,
    male: 0,
    female: 0
  };
  branchCounts: { [key: string]: number } = {};

  constructor(
    private memberService: MemberService,
    private membershipTypeService: MembershipTypeService,
    private membershipService: MembershipService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService,
    private advancedFilterService: AdvancedMemberFilterService,
    private membershipDeleteService: MembershipDeleteService
  ) {
    this.searchTextSubject.pipe(
      debounceTime(750),
      distinctUntilChanged()
    ).subscribe(searchText => {
      this.memberFilterText = searchText;
      this.currentPage = 1;
      this.loadMembers();
    });
  }

  ngOnInit(): void {
    this.getBranches();
    this.loadBranchPackageFilters();
    this.loadMultipleMembershipsData();
    this.loadMembers();
    this.getTotalActiveMembers();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.initGenderChart();
    }, 500);
  }

  ngOnDestroy(): void {
    this.searchTextSubject.complete();
    if (this.genderChart) {
      this.genderChart.destroy();
    }
  }

  initGenderChart(): void {
    const canvas = document.getElementById('genderChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    if (this.genderChart) {
      this.genderChart.destroy();
    }

    const chartData = {
      labels: ['Erkek', 'Kadın'],
      datasets: [{
        data: [this.genderCounts.male, this.genderCounts.female],
      backgroundColor: [
        'rgba(67, 97, 238, 0.7)',  // Erkek - Mavi
        'rgba(255, 105, 180, 0.7)'   // Kadın - Pembe
      ],
      borderColor: [
        'rgb(67, 97, 238)',
        'rgb(255, 105, 180)'
        ],
        borderWidth: 1,
        hoverOffset: 4
      }]
    };

    const config: ChartConfiguration = {
      type: 'doughnut' as ChartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              pointStyle: 'circle',
              font: {
                size: 12
              }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw as number;
                const total = (context.dataset.data as number[]).reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        },
        animation: {
          duration: 1000
        },
        layout: {
          padding: {
            top: 10,
            bottom: 20
          }
        }
      }
    };

    this.genderChart = new Chart(ctx, config);
  }

  searchTextChanged(text: string) {
    this.searchTextSubject.next(text);
  }

  onFilterChange(): void {
    this.currentPage = 1;
    this.loadMembers();
    this.getTotalActiveMembers();
  }



  /**
   * Gelişmiş filtreleri sıfırlar
   */
  resetAdvancedFilters(): void {
    this.selectedBranch = '';
    this.selectedPackageGroup = '';
    this.selectedMembershipTypeID = null;
    this.availablePackages = [];
    this.showPackageFilters = false;
    this.branchFilter = '';
    this.expandedBranch = '';
    this.onFilterChange();
  }

  /**
   * Branş bazlı paket filtreleme bilgilerini yükler
   */
  loadBranchPackageFilters(): void {
    this.advancedFilterService.getBranchPackageFilters().subscribe({
      next: (response) => {
        if (response.success) {
          this.branchPackageFilters = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading branch package filters:', error);
      }
    });
  }

  /**
   * Branş seçildiğinde paket filtrelerini gösterir
   */
  onBranchSelect(branch: string): void {
    this.selectedBranch = branch;
    this.branchFilter = branch;
    this.expandedBranch = ''; // Accordion'ı kapat

    // Seçilen branşın paketlerini bul
    const branchData = this.branchPackageFilters.find(b => b.branch === branch);
    if (branchData) {
      this.availablePackages = branchData.packages;
      this.showPackageFilters = true;
    } else {
      this.availablePackages = [];
      this.showPackageFilters = false;
    }

    // Paket filtrelerini sıfırla
    this.selectedPackageGroup = '';
    this.selectedMembershipTypeID = null;

    this.onFilterChange();
  }

  /**
   * Paket grubu seçildiğinde filtreleme yapar
   */
  onPackageGroupSelect(packageGroup: string): void {
    this.selectedPackageGroup = packageGroup;

    // Seçilen paket grubuna ait üyelik türlerini bul
    const packagesInGroup = this.availablePackages.filter(p => p.packageGroup === packageGroup);
    if (packagesInGroup.length === 1) {
      this.selectedMembershipTypeID = packagesInGroup[0].membershipTypeID;
    } else {
      this.selectedMembershipTypeID = null;
    }

    this.onFilterChange();
  }

  /**
   * Belirli bir üyelik türü seçildiğinde filtreleme yapar
   */
  onMembershipTypeSelect(membershipTypeID: number): void {
    this.selectedMembershipTypeID = membershipTypeID;

    // Seçilen üyelik türünün paket grubunu bul
    const selectedPackage = this.availablePackages.find(p => p.membershipTypeID === membershipTypeID);
    if (selectedPackage) {
      this.selectedPackageGroup = selectedPackage.packageGroup;
    }

    this.onFilterChange();
  }

  /**
   * Benzersiz paket gruplarını döndürür
   */
  getUniquePackageGroups(): { packageGroup: string, totalCount: number }[] {
    const groups = new Map<string, number>();

    this.availablePackages.forEach(pkg => {
      const current = groups.get(pkg.packageGroup) || 0;
      groups.set(pkg.packageGroup, current + pkg.memberCount);
    });

    return Array.from(groups.entries()).map(([packageGroup, totalCount]) => ({
      packageGroup,
      totalCount
    }));
  }

  /**
   * Seçilen paket grubuna göre filtrelenmiş paketleri döndürür
   */
  getFilteredPackages(): PackageFilter[] {
    if (!this.selectedPackageGroup) {
      return this.availablePackages;
    }

    return this.availablePackages.filter(p => p.packageGroup === this.selectedPackageGroup);
  }

  /**
   * Üyenin çoklu üyeliği olup olmadığını kontrol eder
   */
  hasMultipleMemberships(memberId: number): boolean {
    return this.multipleMembershipsMap.get(memberId) || false;
  }

  /**
   * Çoklu üyelik bilgilerini yükler
   */
  loadMultipleMembershipsData(): void {
    this.advancedFilterService.getMembersWithMultipleMemberships().subscribe({
      next: (response) => {
        if (response.success) {
          // Çoklu üyeliği olan üyeleri map'e ekle
          this.multipleMembershipsMap.clear();
          response.data.forEach(member => {
            if (member.activeMemberships.length > 1) {
              this.multipleMembershipsMap.set(member.memberID, true);
            }
          });
        }
      },
      error: (error) => {
        console.error('Error loading multiple memberships data:', error);
      }
    });
  }

  /**
   * Toplam üye sayısını döndürür
   */
  getTotalMembers(): number {
    return this.branchPackageFilters.reduce((total, branch) => total + branch.totalMembers, 0);
  }

  /**
   * Branş accordion'ını aç/kapat
   */
  toggleBranchAccordion(branch: string): void {
    if (this.expandedBranch === branch) {
      this.expandedBranch = '';
    } else {
      this.expandedBranch = branch;
      // Eğer accordion açılıyorsa ve henüz bu branş seçili değilse, branşı seç
      if (this.branchFilter !== branch && this.selectedBranch !== branch) {
        this.selectBranchOnly(branch);
      }
    }
  }

  /**
   * Sadece branş seçimi (paket seçimi olmadan)
   */
  selectBranchOnly(branch: string): void {
    this.branchFilter = branch;
    this.selectedBranch = branch;
    this.selectedMembershipTypeID = null;
    this.selectedPackageGroup = '';
    this.onFilterChange();
  }

  /**
   * Spesifik paket seçimi
   */
  selectSpecificPackage(branch: string, membershipTypeID: number): void {
    this.branchFilter = branch;
    this.selectedBranch = branch;
    this.selectedMembershipTypeID = membershipTypeID;
    this.selectedPackageGroup = '';
    this.onFilterChange();
  }

  /**
   * Seçilen paket adını döndürür
   */
  getSelectedPackageName(): string {
    if (!this.selectedMembershipTypeID) return '';

    for (const branch of this.branchPackageFilters) {
      const pkg = branch.packages.find(p => p.membershipTypeID === this.selectedMembershipTypeID);
      if (pkg) {
        return pkg.typeName;
      }
    }
    return '';
  }

  /**
   * Tüm branşları seç (filtreleri temizle)
   */
  selectAllBranches(): void {
    this.branchFilter = '';
    this.selectedBranch = '';
    this.selectedMembershipTypeID = null;
    this.selectedPackageGroup = '';
    this.expandedBranch = '';
    this.onFilterChange();
  }

  /**
   * Tüm filtreleri sıfırla
   */
  resetAllFilters(): void {
    this.branchFilter = '';
    this.selectedBranch = '';
    this.selectedMembershipTypeID = null;
    this.selectedPackageGroup = '';
    this.expandedBranch = '';
    this.showPackageFilters = false;
    this.availablePackages = [];
    this.onFilterChange();
  }
  openFreezeDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(FreezeMembershipDialogComponent, {
      width: '400px',
      data: { 
        memberName: member.name,
        membershipID: member.membershipID
      }
    });

    dialogRef.afterClosed().subscribe(freezeDays => {
      if (freezeDays) {
        this.membershipService.freezeMembership(member.membershipID, freezeDays).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Üyelik başarıyla donduruldu');
              this.loadMembers();
            } else {
              this.toastrService.error(response.message);
            }
          },
          error: (error) => {
            this.toastrService.error('Üyelik dondurulurken bir hata oluştu');
          }
        });
      }
    });
  }
  getTotalActiveMembers() {
    this.memberService.getTotalActiveMembers().subscribe({
      next: (response) => {
        if (response.success) {
          this.totalActiveMembers = response.data;
          this.activeMembersCount = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching total members:', error);
      },
    });
  }

  loadMembers() {
    this.isLoading = true;
    const gender = this.genderFilter ? parseInt(this.genderFilter) : undefined;

    // Tek filtreleme sistemi - gelişmiş parametreleri her zaman kullan
    this.memberService
      .getMemberDetailsPaginated(
        this.currentPage,
        this.memberFilterText,
        gender,
        this.branchFilter,
        this.selectedMembershipTypeID || undefined,
        this.selectedPackageGroup || undefined
      )
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.members = response.data.data;
            this.totalPages = response.data.totalPages;
            this.totalItems = response.data.totalCount;
            this.calculateActiveMembersCount();
            this.calculateFilterCounts();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching members:', error);
          this.toastrService.error(
            'Üyeler yüklenirken bir hata oluştu.',
            'Hata'
          );
          this.isLoading = false;
        },
      });
  }

  calculateFilterCounts() {
    this.genderCounts.all = this.totalItems;
  
    this.memberService.getActiveMemberCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.genderCounts.male = response.data['male'];
          this.genderCounts.female = response.data['female'];
          
          // Cinsiyet grafiğini güncelle
          if (this.genderChart) {
            this.genderChart.data.datasets[0].data = [
              this.genderCounts.male, 
              this.genderCounts.female
            ];
            this.genderChart.update();
          } else {
            this.initGenderChart();
          }
        }
      },
      error: (error) => {
        console.error('Error fetching gender counts:', error);
      }
    });
  
    this.memberService.getBranchCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.branchCounts = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching branch counts:', error);
      }
    });
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadMembers();
    }
  }

  getBranches() {
    this.membershipTypeService.getMembershipTypes().subscribe((response) => {
      this.membershipTypes = this.getUniqueBranches(response.data);
    });
  }

  getUniqueBranches(membershipTypes: MembershipType[]): MembershipType[] {
    const uniqueBranches: MembershipType[] = [];
    const branchMap = new Map<string, boolean>();

    membershipTypes.forEach((type) => {
      if (!branchMap.has(type.branch)) {
        branchMap.set(type.branch, true);
        uniqueBranches.push(type);
      }
    });

    return uniqueBranches;
  }

  calculateActiveMembersCount() {
    this.activeMembersCount = this.members.filter((member) => {
      return member.remainingDays >= 0;
    }).length;
  }

  openUpdateDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(MembershipUpdateComponent, {
      width: '400px',
      data: {
        membershipID: member.membershipID,
        memberID: member.memberID,
        membershipTypeID: member.membershipTypeID,
        startDate: member.startDate,
        endDate: member.endDate,
        name: member.name,
        branch: member.branch,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadMembers();
      }
    });
  }

  deleteMember(member: MemberFilter) {
    // Akıllı silme dialog'unu aç
    const dialogRef = this.dialog.open(SmartDeleteDialogComponent, {
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      data: { memberId: member.memberID },
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Silme işlemi başarılı, listeyi yenile
        this.loadMembers();
        this.getTotalActiveMembers();
        this.loadBranchPackageFilters(); // Filtreleme verilerini de güncelle
      }
    });
  }
}