export interface MembershipInfo {
  branch: string;
  startDate: string;
  endDate: string;
  remainingDays: number;
}

export interface MemberQRInfo {
  name: string;
  scanNumber: string;
  remainingDays: string;
  memberships: MembershipInfo[];
  isFrozen: boolean;
  freezeEndDate?: Date;
  membershipStatus?: string; // Eski alan korundu
  phoneNumber?: string; // Telefon numarası alanı eklendi
}

export interface MemberQRInfoResponse {
  data: MemberQRInfo;
  message: string;
  success: boolean;
}