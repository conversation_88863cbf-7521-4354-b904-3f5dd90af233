using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    /// <summary>
    /// Üyenin tüm aktif üyeliklerini listeleyen DTO
    /// Güvenli silme işlemi için kullanılır
    /// </summary>
    public class MemberActiveMembershipsDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public int Gender { get; set; }
        public List<MembershipDeleteOptionDto> ActiveMemberships { get; set; }
        public int TotalActiveMemberships { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public bool HasMultipleMemberships { get; set; }
    }

    /// <summary>
    /// Silinebilir üyelik seçeneği DTO'su
    /// </summary>
    public class MembershipDeleteOptionDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public int Day { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsFrozen { get; set; }
        public DateTime? FreezeEndDate { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public int PaymentCount { get; set; }
        public DateTime LastPaymentDate { get; set; }
        public string PaymentMethods { get; set; } // "Nakit, Kredi Kartı" gibi
        public bool CanBeDeleted { get; set; }
        public string DeleteWarning { get; set; }
    }

    /// <summary>
    /// Üyelik silme isteği DTO'su
    /// </summary>
    public class MembershipDeleteRequestDto : IDto
    {
        public int MemberID { get; set; }
        public List<int> MembershipIDsToDelete { get; set; }
        public bool DeleteAllMemberships { get; set; }
        public string DeleteReason { get; set; }
        public bool ConfirmPaymentLoss { get; set; } // Ödeme kaybını onaylıyor mu
    }

    /// <summary>
    /// Üyelik silme sonucu DTO'su
    /// </summary>
    public class MembershipDeleteResultDto : IDto
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int DeletedMembershipsCount { get; set; }
        public decimal RefundableAmount { get; set; }
        public List<string> DeletedMembershipDetails { get; set; }
        public List<string> Warnings { get; set; }
        public bool RequiresManagerApproval { get; set; }
    }
}
