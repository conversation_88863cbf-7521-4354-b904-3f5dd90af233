.content-blur {
  filter: blur(2px);
  pointer-events: none;
}

.table thead th {
  background-color: #4a7299;
  color: white;
  padding: 12px;
}

.table td {
  vertical-align: middle;
  padding: 10px;
}

.badge {
  padding: 8px 12px;
  font-size: 0.9em;
  cursor: pointer;
  transition: all 0.3s ease;
}

.badge:hover {
  opacity: 0.8;
}

.search-container {
  min-width: 250px;
}

.total-amount {
  font-size: 1.1em;
  color: #dc3545;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.member-group {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.member-group .bg-light {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.product-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.product-summary .badge {
  font-size: 0.85em;
  background-color: rgba(0,123,255,0.1) !important;
  color: #0056b3 !important;
  border: 1px solid rgba(0,123,255,0.2);
  padding: 4px 8px;
}

/* Toplam Borç Styling */
.total-debt-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--bg-secondary, #f8f9fa);
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.total-debt-label {
  font-weight: 600;
  color: var(--text-primary, #495057);
}

.total-debt-amount {
  font-weight: 700;
  font-size: 1.1em;
  color: var(--danger, #dc3545);
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

/* Dark mode compatibility */
@media (prefers-color-scheme: dark) {
  .total-debt-amount {
    color: var(--danger, #ff6b6b);
  }
  
  .total-debt-label {
    color: var(--text-primary, #e9ecef);
  }
  
  .total-debt-container {
    background-color: var(--bg-secondary, #343a40);
  }
}

/* Hover effect */
.total-debt-container:hover .total-debt-amount {
  transform: scale(1.05);
}

.summary-box .card {
  transition: transform 0.2s ease;
}

.summary-box .card:hover {
  transform: translateY(-2px);
}

.month-filter input {
  min-width: 150px;
}

.calendar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 4px;
  color: #495057;
}

.calendar-icon i {
  font-size: 16px;
}

/* Responsive tasarım için ek stiller */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 1rem;
  }

  .d-flex.align-items-center {
    flex-direction: column;
    width: 100%;
  }

  .search-container,
  .month-filter {
    width: 100%;
  }

  .product-summary {
    margin-bottom: 0.5rem;
  }

  .total-amount {
    margin-bottom: 0.5rem;
  }
}

/* Segmented Control Styling */
.transaction-view-selector {
  padding: 0 var(--spacing-lg); /* Align with card body horizontal padding */
  margin-top: var(--spacing-md); /* Add space below header */
  margin-bottom: var(--spacing-md); /* Add space above card body */
  display: flex; /* Center the control */
  justify-content: center; /* Center the control */
}

.segmented-control {
  display: inline-flex; /* Use inline-flex to wrap buttons */
  background-color: var(--bg-tertiary); /* Slightly different background */
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xs); /* Small padding around buttons */
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); /* Inner shadow for depth */
}

.segment-button {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background-color: transparent; /* Initially transparent */
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  border-radius: var(--border-radius-sm); /* Slightly rounded corners for buttons */
  transition: background-color var(--transition-speed) var(--transition-timing),
              color var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1; /* Make buttons take equal space if needed */
  text-align: center;
}

.segment-button:hover:not(.active) {
  background-color: rgba(var(--primary-rgb), 0.05); /* Subtle hover effect */
  color: var(--text-primary);
}

.segment-button.active {
  background-color: var(--white); /* Active button background */
  color: var(--primary); /* Active button text color */
  font-weight: 600;
  box-shadow: var(--shadow-sm); /* Shadow for active button */
}

.segment-button i {
  transition: color var(--transition-speed) var(--transition-timing);
}

/* Icon colors */
.segment-button.active i {
  color: var(--primary);
}

.segment-button:not(.active) i {
  color: var(--text-secondary);
}
.segment-button:hover:not(.active) i {
   color: var(--text-primary);
}


/* Dark Mode Adjustments for Segmented Control */
[data-theme="dark"] .segmented-control {
  background-color: var(--bg-secondary); /* Darker background for container */
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .segment-button {
  color: var(--text-secondary);
}

[data-theme="dark"] .segment-button:hover:not(.active) {
  background-color: rgba(var(--primary-rgb), 0.1); /* Adjusted hover for dark */
  color: var(--text-primary);
}

[data-theme="dark"] .segment-button.active {
  background-color: var(--bg-tertiary); /* Active button background in dark */
  color: var(--primary); /* Primary color adjusted for dark mode */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .segment-button.active i {
  color: var(--primary);
}

[data-theme="dark"] .segment-button:not(.active) i {
  color: var(--text-secondary);
}

[data-theme="dark"] .segment-button:hover:not(.active) i {
   color: var(--text-primary);
}