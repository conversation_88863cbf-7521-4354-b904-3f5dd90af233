.smart-delete-dialog {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;

  .loading-container {
    text-align: center;
    padding: 2rem;
  }

  .dialog-content {
    padding: 1.5rem;
  }

  .dialog-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;

    .dialog-title {
      margin: 0;
      color: var(--text-primary);
      font-weight: 600;
    }

    .dialog-subtitle {
      margin: 0.5rem 0 0 0;
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
  }

  .member-info-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-weight: 500;
        color: var(--text-secondary);
      }

      .info-value {
        font-weight: 600;
        color: var(--text-primary);
      }
    }
  }

  .membership-selection {
    margin-bottom: 1.5rem;

    .section-title {
      margin-bottom: 1rem;
      color: var(--text-primary);
      font-weight: 600;
    }

    .select-all-option {
      margin-bottom: 1rem;
      padding: 0.75rem;
      background: var(--bg-light);
      border-radius: 6px;
    }

    .membership-list {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid var(--border-color);
      border-radius: 8px;
    }

    .membership-item {
      display: flex;
      align-items: flex-start;
      padding: 1rem;
      border-bottom: 1px solid var(--border-color);
      cursor: pointer;
      transition: all 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: var(--bg-light);
      }

      &.selected {
        background: var(--primary-light);
        border-color: var(--primary);
      }

      &.warning {
        border-left: 4px solid var(--warning);
      }

      .membership-checkbox {
        margin-right: 1rem;
        margin-top: 0.25rem;
      }

      .membership-details {
        flex: 1;

        .membership-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.75rem;

          .membership-title {
            margin: 0;
            font-weight: 600;
            color: var(--text-primary);
          }
        }

        .membership-info {
          .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.5rem;
            margin-bottom: 0.75rem;

            .info-item {
              .info-label {
                display: block;
                color: var(--text-secondary);
                font-size: 0.8rem;
                margin-bottom: 0.25rem;
              }

              .info-value {
                font-weight: 500;
                color: var(--text-primary);
              }
            }
          }

          .freeze-status {
            margin-bottom: 0.5rem;

            small {
              display: block;
              margin-top: 0.25rem;
              color: var(--text-secondary);
            }
          }

          .payment-info {
            margin-bottom: 0.5rem;
          }

          .delete-warning {
            padding: 0.5rem;
            background: var(--warning-light);
            border-radius: 4px;
            border-left: 4px solid var(--warning);
          }
        }
      }
    }
  }

  .delete-form {
    margin-bottom: 1.5rem;

    .form-group {
      margin-bottom: 1rem;

      .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-primary);

        &.required::after {
          content: ' *';
          color: var(--danger);
        }
      }

      .modern-form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background: var(--input-bg);
        color: var(--text-primary);
        font-size: 0.9rem;
        transition: border-color 0.2s ease;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-light);
        }
      }

      .payment-loss-warning {
        padding: 0.75rem;
        background: var(--danger-light);
        border: 1px solid var(--danger);
        border-radius: 6px;
        color: var(--danger-dark);
        margin-bottom: 0.5rem;
      }
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
  }

  .result-content {
    padding: 2rem;
    text-align: center;

    .result-header {
      margin-bottom: 2rem;

      .result-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }

      .result-title {
        margin-bottom: 0.5rem;
        color: var(--text-primary);
      }

      .result-message {
        color: var(--text-secondary);
        margin: 0;
      }
    }

    .result-details {
      text-align: left;
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;

      .detail-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.75rem;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-label {
          font-weight: 500;
          color: var(--text-secondary);
        }

        .detail-value {
          font-weight: 600;
          color: var(--text-primary);
        }
      }

      .deleted-memberships,
      .warnings {
        margin-top: 1.5rem;

        h6 {
          margin-bottom: 0.75rem;
          color: var(--text-primary);
        }

        ul {
          margin: 0;
          padding-left: 1.5rem;

          li {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .warning-list li {
          color: var(--warning-dark);
        }
      }

      .manager-approval {
        margin-top: 1.5rem;

        .modern-alert {
          padding: 0.75rem;
          border-radius: 6px;
          border: 1px solid;

          &.modern-alert-warning {
            background: var(--warning-light);
            border-color: var(--warning);
            color: var(--warning-dark);
          }
        }
      }
    }

    .result-actions {
      display: flex;
      justify-content: center;
    }
  }
}

// Dark mode support
[data-theme="dark"] {
  .smart-delete-dialog {
    .membership-item {
      &.selected {
        background: rgba(var(--primary-rgb), 0.1);
      }
    }

    .payment-loss-warning {
      background: rgba(var(--danger-rgb), 0.1);
      color: var(--danger-light);
    }

    .delete-warning {
      background: rgba(var(--warning-rgb), 0.1);
    }

    .modern-alert-warning {
      background: rgba(var(--warning-rgb), 0.1);
      color: var(--warning-light);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .smart-delete-dialog {
    .membership-selection {
      .membership-item {
        .membership-details {
          .info-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }

    .dialog-actions {
      flex-direction: column;

      .modern-btn {
        width: 100%;
      }
    }
  }
}
