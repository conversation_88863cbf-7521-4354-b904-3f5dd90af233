export interface MemberDetailWithHistory {
  memberID: number;
  userID?: number; // Profil fotoğrafı için eklendi
  name: string;
  gender: number;
  phoneNumber: string;
  adress: string;
  birthDate?: Date;
  email: string;
  isActive?: boolean;
  scanNumber: string;
  balance: number;
  creationDate?: Date;
  remainingDays?: number; // <PERSON>lan gün sayısı eklendi
  lastMembershipEndDate?: Date; // Son üyelik bitiş tarihi

  // Üyelik geçmişi
  memberships: MembershipHistory[];

  // Ödeme geçmişi
  payments: PaymentHistoryItem[];

  // Giriş/Çıkış geçmişi
  entryExitHistory: EntryExitHistoryItem[];

  // Üyelik dondurma geçmişi
  freezeHistory: MembershipFreezeHistoryItem[];
}

export interface MembershipHistory {
  membershipID: number;
  membershipTypeID: number;
  typeName: string;
  branch: string;
  day: number;
  price: number;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  isFrozen: boolean;
  freezeStartDate?: Date;
  freezeEndDate?: Date;
  freezeDays?: number;
  originalEndDate?: Date;
  creationDate?: Date;
}

export interface PaymentHistoryItem {
  paymentID: number;
  membershipID: number;
  membershipTypeName: string;
  branch: string;
  paymentDate: Date;
  paymentAmount: number;
  paymentMethod: string;
  paymentStatus: string;
}

export interface EntryExitHistoryItem {
  entryExitID: number;
  membershipID: number;
  membershipTypeName: string;
  branch: string;
  entryDate?: Date;
  exitDate?: Date;
}

export interface MembershipFreezeHistoryItem {
  freezeHistoryID: number;
  membershipID: number;
  membershipTypeName: string;
  branch: string;
  startDate: Date;
  plannedEndDate: Date;
  actualEndDate?: Date;
  freezeDays: number;
  usedDays?: number;
  cancellationType: string;
}
